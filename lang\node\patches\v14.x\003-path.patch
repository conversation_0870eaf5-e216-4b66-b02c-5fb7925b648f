--- a/lib/internal/modules/cjs/loader.js
+++ b/lib/internal/modules/cjs/loader.js
@@ -1231,7 +1231,8 @@ Module._initPaths = function() {
     path.resolve(process.execPath, '..') :
     path.resolve(process.execPath, '..', '..');
 
-  let paths = [path.resolve(prefixDir, 'lib', 'node')];
+  let paths = [path.resolve(prefixDir, 'lib', 'node'),
+               path.resolve(prefixDir, 'lib', 'node_modules')];
 
   if (homeDir) {
     ArrayPrototypeUnshift(paths, path.resolve(homeDir, '.node_libraries'));
