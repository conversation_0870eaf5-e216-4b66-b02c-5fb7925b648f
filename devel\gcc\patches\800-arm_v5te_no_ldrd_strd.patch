commit b050f87d13b5dc7ed82feb9a90f4529de58bdf25
Author: <PERSON> <<EMAIL>>
Date:   Wed Feb 19 19:20:10 2014 +0000

    gcc: prevent the use of LDRD/STRD on ARMv5TE
    
    These instructions are for 64-bit load/store. On ARMv5TE, the CPU
    requires addresses to be aligned to 64-bit. When misaligned, behavior is
    undefined (effectively either loads the same word twice on LDRD, or
    corrupts surrounding memory on STRD).
    
    On ARMv6 and newer, unaligned access is safe.
    
    Removing these instructions for ARMv5TE is necessary, because GCC
    ignores alignment information in pointers and does unsafe optimizations
    that have shown up as bugs in various places.
    
    Signed-off-by: <PERSON> <<EMAIL>>
    
    SVN-Revision: 39638

--- a/gcc/config/arm/arm.h
+++ b/gcc/config/arm/arm.h
@@ -150,7 +150,7 @@ extern tree arm_fp16_type_node;
 /* Thumb-1 only.  */
 #define TARGET_THUMB1_ONLY		(TARGET_THUMB1 && !arm_arch_notm)
 
-#define TARGET_LDRD			(arm_arch5e && ARM_DOUBLEWORD_ALIGN \
+#define TARGET_LDRD			(arm_arch6 && ARM_DOUBLEWORD_ALIGN \
                                          && !TARGET_THUMB1)
 
 #define TARGET_CRC32			(arm_arch_crc)
