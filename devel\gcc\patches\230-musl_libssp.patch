commit 1877bc9d8f2be143fbe530347a945850d0ecd234
Author: <PERSON> <<EMAIL>>
Date:   Mon Jun 22 10:31:07 2015 +0000

    gcc/musl: rework SSP-support
    
    Make musl provide libssp_nonshared.a and make GCC link it unconditionally
    if musl is used. This should be a no-op if SSP is disabled and seems to be
    the only reliable way of dealing with SSP over all packages due to the mess
    that is linkerflags handling in packages.
    
    Signed-off-by: <PERSON> <<EMAIL>>
    
    SVN-Revision: 46108

--- a/gcc/gcc.c
+++ b/gcc/gcc.c
@@ -861,7 +861,9 @@ proper position among the other output f
 #endif
 
 #ifndef LINK_SSP_SPEC
-#ifdef TARGET_LIBC_PROVIDES_SSP
+#if DEFAULT_LIBC == LIBC_MUSL
+#define LINK_SSP_SPEC "-lssp_nonshared"
+#elif defined(TARGET_LIBC_PROVIDES_SSP)
 #define LINK_SSP_SPEC "%{fstack-protector|fstack-protector-all" \
 		       "|fstack-protector-strong|fstack-protector-explicit:}"
 #else
