--- a/ext/Errno/Errno_pm.PL
+++ b/ext/Errno/Errno_pm.PL
@@ -133,7 +133,7 @@ sub get_files {
 	# Some Linuxes have weird errno.hs which generate
 	# no #file or #line directives
 	my ($linux_errno_h) = grep { -e $_ } map { "$_/errno.h" }
-	    "$sysroot/usr/include", "$sysroot/usr/local/include",
+	    "$sysroot/usr/include", "$sysroot/usr/local/include", "$sysroot/include",
 	    split / / => $Config{locincpth} or
 		die "Cannot find errno.h";
 	$file{$linux_errno_h} = 1;
