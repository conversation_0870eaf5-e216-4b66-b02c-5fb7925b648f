--- a/Makefile.PL
+++ b/Makefile.PL
@@ -1,7 +1,6 @@
 # Do yourself a favour, and don't edit this file, see README for build instructions
 
 use ExtUtils::MakeMaker;
-use Devel::CheckLib;
 
 my @inc_search = qw(/opt/local/include /usr/local/include);
 my @lib_search = qw(/opt/local/lib64 /usr/local/lib64 /opt/local/lib /usr/local/lib);
@@ -13,28 +12,13 @@ unless (exists $args{INC} or exists $arg
 
   my @incpath = grep {-d} @inc_search;
   my @libpath = grep {-d} @lib_search;
-  my $have_sasl2 = eval {
-    assert_lib(
-      lib     => "sasl2",
-      header  => "sasl/sasl.h",
-      libpath => \@libpath,
-      incpath => \@incpath
-    );
-    1;
-  };
+  my $have_sasl2 = 1;
 
   if ($have_sasl2) {
     $mmopt{DEFINE} = "-DSASL2" unless $use_sasl2;
   }
   else {
     exit(0) if $use_sasl2;
-    @incpath = grep {-d} map { ("$_/sasl", $_) } @inc_search;
-    check_lib_or_exit(
-      lib     => "sasl",
-      header  => "sasl.h",
-      libpath => \@libpath,
-      incpath => \@incpath
-    );
   }
 
   $mmopt{INC}  = join " ", map {"-I$_"} @incpath;
@@ -58,7 +42,6 @@ WriteMakefile(
           repository => 'http://github.com/gbarr/perl-authen-sasl-xs',
         },
         build_requires => {
-          'Devel::CheckLib' => 0,
         },
       }
       )
