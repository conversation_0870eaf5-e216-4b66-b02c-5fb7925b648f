--- a/configure
+++ b/configure
@@ -388,18 +388,22 @@ then
 fi
 
 echo_n "Configuring for system... "
-if uname -s
-then
-   LUAROCKS_UNAME_S=`uname -s`
-else
-   die "Could not determine operating system. 'uname -s' failed."
+if [ -z "$LUAROCKS_UNAME_S" ]; then
+    if uname -s
+    then
+        LUAROCKS_UNAME_S=`uname -s`
+    else
+        die "Could not determine operating system. 'uname -s' failed."
+    fi
 fi
 echo_n "Configuring for architecture... "
-if uname -m
-then
-   LUAROCKS_UNAME_M=`uname -m`
-else
-   die "Could not determine processor architecture. 'uname -m' failed."
+if [ -z "$LUAROCKS_UNAME_M" ]; then
+    if uname -m
+    then
+        LUAROCKS_UNAME_M=`uname -m`
+    else
+        die "Could not determine processor architecture. 'uname -m' failed."
+    fi
 fi
 
 for v in 5.1 5.2 5.3; do
