# This is the configuration for charts.d.plugin

# Each of its collectors can read configuration either from this file
# or a NAME.conf file (where NAME is the collector name).
# The collector specific file has higher precedence.

# This file is a shell script too.

# -----------------------------------------------------------------------------

# number of seconds to run without restart
# after this time, charts.d.plugin will exit
# netdata will restart it, but a small gap
# will appear in the charts.d.plugin charts.
#restart_timeout=$[3600 * 4]

# when making iterations, charts.d can loop more frequently
# to prevent plugins missing iterations.
# this is a percentage relative to update_every to align its
# iterations.
# The minimum is 10%, the maximum 100%.
# So, if update_every is 1 second and time_divisor is 50,
# charts.d will iterate every 500ms.
# Charts will be called to collect data only if the time
# passed since the last time the collected data is equal or
# above their update_every.
#time_divisor=50

# -----------------------------------------------------------------------------

# the default enable/disable for all charts.d collectors
# the default is "yes"
enable_all_charts="no"

# BY DEFAULT ENABLED MODULES
# ap=yes
# apcupsd=yes
# libreswan=yes
# nut=yes
# opensips=yes

# -----------------------------------------------------------------------------
# THESE NEED TO BE SET TO "force" TO BE ENABLED

# Nothing useful.
# Just an example charts.d plugin you can use as a template.
# example=force
sensors=force
