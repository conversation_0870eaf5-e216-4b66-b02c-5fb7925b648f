From 71607715f11c9875a5aaaf3240885c45f79138e9 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 17 Aug 2018 13:35:40 +0200
Subject: [PATCH] Fix swapping fake lines in pch_swap

* src/pch.c (pch_swap): Fix swapping p_bfake and p_efake when there is a
blank line in the middle of a context-diff hunk: that empty line stays
in the middle of the hunk and isn't swapped.

Fixes: https://savannah.gnu.org/bugs/index.php?53133
---
 src/pch.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/src/pch.c
+++ b/src/pch.c
@@ -2115,7 +2115,7 @@ pch_swap (void)
     }
     if (p_efake >= 0) {			/* fix non-freeable ptr range */
 	if (p_efake <= i)
-	    n = p_end - i + 1;
+	    n = p_end - p_ptrn_lines;
 	else
 	    n = -i;
 	p_efake += n;
