From 85a3e5b4f65e5284e59dcdd90e92ea7d50ef6907 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sun, 8 Feb 2015 17:23:13 +0100
Subject: [PATCH] erts/emulator: reorder inclued headers paths

If the Perl Compatible Regular Expressions is installed on the
host and the path to the headers is added to the CFLAGS, the
pcre.h from the host is used instead of the one provided by
erlang.

Erlang use an old version of this file which is incompatible
with the upstream one.

Move INCLUDES before CFLAGS to use pcre.h from erlang.

http://autobuild.buildroot.net/results/cbd/cbd8b54eef535f19d7d400fd269af1b3571d6143/build-end.log

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
[Bernd: rebased for erlang-21.0]
Signed-off-by: <PERSON><PERSON> <<EMAIL>>
[sv<PERSON><PERSON><PERSON>: updated for erlang-24.2]
---
 erts/emulator/Makefile.in | 8 ++++----
 1 file changed, 4 insertions(+), 4 deletions(-)

--- a/erts/emulator/Makefile.in
+++ b/erts/emulator/Makefile.in
@@ -800,7 +800,7 @@ endif
 # Usually the same as the default rule, but certain platforms (e.g. win32) mix
 # different compilers
 $(OBJDIR)/beam_emu.o: beam/emu/beam_emu.c
-	$(V_EMU_CC) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) $(INCLUDES) -c $< -o $@
+	$(V_EMU_CC) $(INCLUDES) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) -c $< -o $@
 
 $(OBJDIR)/beam_emu.S: beam/emu/beam_emu.c
 	$(V_EMU_CC) -S -fverbose-asm $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) $(INCLUDES) -c $< -o $@
@@ -863,13 +863,13 @@ endif
 # General targets
 #
 $(OBJDIR)/%.o: beam/%.c
-	$(V_CC) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) $(INCLUDES) -c $< -o $@
+	$(V_CC) $(INCLUDES) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) -c $< -o $@
 
 $(OBJDIR)/%.o: beam/emu/%.c
-	$(V_CC) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) $(INCLUDES) -c $< -o $@
+	$(V_CC) $(INCLUDES) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) -c $< -o $@
 
 $(OBJDIR)/%.o: beam/jit/%.c
-	$(V_CC) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) $(INCLUDES) -c $< -o $@
+	$(V_CC) $(INCLUDES) $(subst -O2, $(GEN_OPT_FLGS), $(CFLAGS)) -c $< -o $@
 
 $(OBJDIR)/%.o: $(TARGET)/%.c
 	$(V_CC) $(CFLAGS) $(INCLUDES) -Idrivers/common -c $< -o $@
