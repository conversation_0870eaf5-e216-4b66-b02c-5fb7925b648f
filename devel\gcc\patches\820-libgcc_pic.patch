commit c96312958c0621e72c9b32da5bc224ffe2161384
Author: <PERSON> <<EMAIL>>
Date:   Mon Oct 19 23:26:09 2009 +0000

    gcc: create a proper libgcc_pic.a static library for relinking (4.3.3+ for now, backport will follow)
    
    SVN-Revision: 18086

--- a/libgcc/Makefile.in
+++ b/libgcc/Makefile.in
@@ -920,11 +920,12 @@ $(libgcov-driver-objects): %$(objext): $
 
 # Static libraries.
 libgcc.a: $(libgcc-objects)
+libgcc_pic.a: $(libgcc-s-objects)
 libgcov.a: $(libgcov-objects)
 libunwind.a: $(libunwind-objects)
 libgcc_eh.a: $(libgcc-eh-objects)
 
-libgcc.a libgcov.a libunwind.a libgcc_eh.a:
+libgcc.a libgcov.a libunwind.a libgcc_eh.a libgcc_pic.a:
 	-rm -f $@
 
 	objects="$(objects)";					\
@@ -945,7 +946,7 @@ all: libunwind.a
 endif
 
 ifeq ($(enable_shared),yes)
-all: libgcc_eh.a libgcc_s$(SHLIB_EXT)
+all: libgcc_eh.a libgcc_pic.a libgcc_s$(SHLIB_EXT)
 ifneq ($(LIBUNWIND),)
 all: libunwind$(SHLIB_EXT)
 libgcc_s$(SHLIB_EXT): libunwind$(SHLIB_EXT)
@@ -1151,6 +1152,10 @@ install-shared:
 	chmod 644 $(DESTDIR)$(inst_libdir)/libgcc_eh.a
 	$(RANLIB) $(DESTDIR)$(inst_libdir)/libgcc_eh.a
 
+	$(INSTALL_DATA) libgcc_pic.a $(mapfile) $(DESTDIR)$(inst_libdir)/
+	chmod 644 $(DESTDIR)$(inst_libdir)/libgcc_pic.a
+	$(RANLIB) $(DESTDIR)$(inst_libdir)/libgcc_pic.a
+
 	$(subst @multilib_dir@,$(MULTIDIR),$(subst \
 		@shlib_base_name@,libgcc_s,$(subst \
 		@shlib_slibdir_qual@,$(MULTIOSSUBDIR),$(SHLIB_INSTALL))))
