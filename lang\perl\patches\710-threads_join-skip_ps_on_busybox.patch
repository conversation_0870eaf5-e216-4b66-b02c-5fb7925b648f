perl: Skip $0 test on busybox

This test requires a ps which provides the -f option, as well as suitable output.
We can't provide either with busybox. Just skip it for now.

Signed-off-by: <PERSON> <<EMAIL>>

--- a/dist/threads/t/join.t
+++ b/dist/threads/t/join.t
@@ -110,36 +110,41 @@ sub skip {
 
 # We parse ps output so this is OS-dependent.
 if ($^O eq 'linux') {
-    # First modify $0 in a subthread.
-    #print "# mainthread: \$0 = $0\n";
-    threads->create(sub{ #print "# subthread: \$0 = $0\n";
-                        $0 = "foobar";
-                        #print "# subthread: \$0 = $0\n"
-                 })->join;
-    #print "# mainthread: \$0 = $0\n";
-    #print "# pid = $$\n";
-    if (open PS, "ps -f |") { # Note: must work in (all) systems.
-        my ($sawpid, $sawexe);
-        while (<PS>) {
-            chomp;
-            #print "# [$_]\n";
-            if (/^\s*\S+\s+$$\s/) {
-                $sawpid++;
-                if (/\sfoobar\s*$/) { # Linux 2.2 leaves extra trailing spaces.
-                    $sawexe++;
-                }
-                last;
-            }
-        }
-        close PS or die;
-        if ($sawpid) {
-            ok($sawpid && $sawexe, 'altering $0 is effective');
-        } else {
-            skip("\$0 check: did not see pid $$ in 'ps -f |'");
-        }
-    } else {
-        skip("\$0 check: opening 'ps -f |' failed: $!");
-    }
+	if (readlink('/bin/ps') ne 'busybox') {
+		# First modify $0 in a subthread.
+		#print "# mainthread: \$0 = $0\n";
+		threads->create(sub{ #print "# subthread: \$0 = $0\n";
+							$0 = "foobar";
+							#print "# subthread: \$0 = $0\n"
+					})->join;
+		#print "# mainthread: \$0 = $0\n";
+		#print "# pid = $$\n";
+		if (open PS, "ps -f |") { # Note: must work in (all) systems.
+			my ($sawpid, $sawexe);
+			while (<PS>) {
+				chomp;
+				#print "# [$_]\n";
+				if (/^\s*\S+\s+$$\s/) {
+					$sawpid++;
+					if (/\sfoobar\s*$/) { # Linux 2.2 leaves extra trailing spaces.
+						$sawexe++;
+					}
+					last;
+				}
+			}
+			close PS or die;
+			if ($sawpid) {
+				ok($sawpid && $sawexe, 'altering $0 is effective');
+			} else {
+				skip("\$0 check: did not see pid $$ in 'ps -f |'");
+			}
+		} else {
+			skip("\$0 check: opening 'ps -f |' failed: $!");
+		}
+	}
+	else {
+		skip("\$0 check: incompatible with busybox");
+	}
 } else {
     skip("\$0 check: only on Linux");
 }
