From 4e3d154c0f1bd975016eaa2d842affd230b736cc Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 21 Feb 2022 14:58:08 +0100
Subject: [PATCH] crypto: do --disable-otp-test-engine for LibreSSL >= 3.5.0

---
 lib/crypto/configure.in | 17 +++++++++++++++--
 1 file changed, 15 insertions(+), 2 deletions(-)

--- a/lib/crypto/configure.in
+++ b/lib/crypto/configure.in
@@ -159,11 +159,11 @@ AS_HELP_STRING([--disable-evp-hmac],
 
 AC_ARG_ENABLE(otp-test-engine,
 AS_HELP_STRING([--disable-otp-test-engine],
-	       [intentionally undocumented workaround]),
+	       [Disable build of the otp_test_engine. (default is --enable-otp-test-engine, unless for LibreSSL >= 3.5.0 where default is --disable-otp-test-engine)]),
 [ case "$enableval" in
     no)   DISABLE_OTP_TEST_ENGINE=yes;;
     *)    DISABLE_OTP_TEST_ENGINE=no;;
-  esac ], DISABLE_OTP_TEST_ENGINE=no)
+  esac ], DISABLE_OTP_TEST_ENGINE=default)
 
 AC_ARG_ENABLE(deprecated_warnings,
 AS_HELP_STRING([--disable-deprecated-warnings],
@@ -245,6 +245,19 @@ yes
 		 ],
 		 [v3_include=yes],
 		 [v3_include=no])
+
+if test "x$DISABLE_OTP_TEST_ENGINE" = "xdefault"; then
+# Get default value for the --disable-otp-test-engine. Depends on cryptolib version
+          AC_EGREP_CPP(^yes.?$,[
+#include <openssl/opensslv.h>
+#if LIBRESSL_VERSION_NUMBER >= 0x3050000fL
+yes
+#endif
+        		 ],
+	                 [DISABLE_OTP_TEST_ENGINE=yes],
+                         [DISABLE_OTP_TEST_ENGINE=no])
+    fi
+
     CPPFLAGS="$save_CPPFLAGS"
 
     if test $valid_include != yes; then
