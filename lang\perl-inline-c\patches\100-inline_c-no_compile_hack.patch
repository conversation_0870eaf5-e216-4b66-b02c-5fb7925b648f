--- a/lib/Inline/C.pm
+++ b/lib/Inline/C.pm
@@ -380,7 +380,7 @@ sub build {
     $o->call('write_XS', 'Build Glue 1');
     $o->call('write_Inline_headers', 'Build Glue 2');
     $o->call('write_Makefile_PL', 'Build Glue 3');
-    $o->call('compile', 'Build Compile');
+    $o->call('compile', 'Build Compile') unless $ENV{'_INLINE_C_NO_COMPILE_'};
     if (IS_WIN32) {
         $lockfh->release or die "releasemutex $file: $^E";
     }
