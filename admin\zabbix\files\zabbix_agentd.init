#!/bin/sh /etc/rc.common
# Copyright (C) 2008-2011 OpenWrt.org

START=60

USE_PROCD=1
PROG=/usr/sbin/zabbix_agentd
CONFIG=/etc/zabbix_agentd.conf

start_service() {
	# Sometimes the agentd config was installed in /etc/zabbix/zabbix_agentd.conf
	[ -f /etc/zabbix/zabbix_agentd.conf ] && mv /etc/zabbix/zabbix_agentd.conf ${CONFIG}

	[ -f ${CONFIG} ] || return 1

	procd_open_instance
	procd_set_param command ${PROG} -c ${CONFIG} -f
	procd_set_param respawn
	procd_set_param stdout 1
	procd_set_param stderr 1
	procd_close_instance
}

