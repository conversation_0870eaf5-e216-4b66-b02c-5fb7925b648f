--- a/Makefile.SH
+++ b/Makefile.SH
@@ -275,6 +275,7 @@ LNS = $lns
 # NOTE: some systems don't grok "cp -f". XXX Configure test needed?
 CPS = $cp
 RMS = rm -f
+RMS_R = rm -rf
 ranlib = $ranlib
 
 # The following are mentioned only to make metaconfig include the
@@ -736,7 +737,7 @@ bitcount.h: generate_uudmap$(HOST_EXE_EX
 	./generate_uudmap$(HOST_EXE_EXT) $(generated_headers)
 
 generate_uudmap$(HOST_EXE_EXT): generate_uudmap$(OBJ_EXT)
-	-@rm generate_uudmap$(HOST_EXE_EXT)
+	@$(RMS) generate_uudmap$(HOST_EXE_EXT)
 	$(LNS) $(HOST_GENERATE) generate_uudmap$(HOST_EXE_EXT)
 
 !NO!SUBS!
@@ -876,26 +877,26 @@ mydtrace.h: $(DTRACE_H)
 	define)
 		$spitshell >>$Makefile <<'!NO!SUBS!'
 $(DTRACE_MINI_O): perldtrace.d $(miniperl_objs_nodt)
-	-rm -rf mpdtrace
+	$(RMS_R) mpdtrace
 	mkdir mpdtrace
 	cp $(miniperl_objs_nodt) mpdtrace/
 	$(DTRACE) -G -s perldtrace.d -o $(DTRACE_MINI_O) $(miniperl_dtrace_objs)
 
 $(DTRACE_PERLLIB_O): perldtrace.d $(perllib_objs_nodt)
-	-rm -rf libpdtrace
+	$(RMS_R) libpdtrace
 	mkdir libpdtrace
 	cp $(perllib_objs_nodt) libpdtrace/
 	$(DTRACE) -G -s perldtrace.d -o $(DTRACE_PERLLIB_O) $(perllib_dtrace_objs)
 
 $(DTRACE_MAIN_O): perldtrace.d perlmain$(OBJ_EXT)
-	-rm -rf maindtrace
+	$(RMS_R) maindtrace
 	mkdir maindtrace
 	cp perlmain$(OBJ_EXT) maindtrace/
 	$(DTRACE) -G -s perldtrace.d -o $(DTRACE_MAIN_O) $(perlmain_dtrace_objs) ||	      \
 	  ( $(ECHO) "No probes in perlmain$(OBJ_EXT), generating a dummy $(DTRACE_MAIN_O)" && \
 	    $(ECHO) >dtrace_main.c &&							      \
 	    `$(CCCMD)` $(PLDLFLAGS) dtrace_main.c &&					      \
-	     rm -f dtrace_main.c )
+	     $(RMS) dtrace_main.c )
 
 !NO!SUBS!
 		;;
@@ -906,13 +907,13 @@ $(LIBPERL): $& $(perllib_dep) $(DYNALOAD
 	case "$useshrplib" in
 	true)
 		$spitshell >>$Makefile <<'!NO!SUBS!'
-	rm -f $@
+	$(RMS) $@
 	$(LD) -o $@ $(SHRPLDFLAGS) $(perllib_objs) $(DYNALOADER) $(libs)
 !NO!SUBS!
 		case "$osname" in
 		aix)
 			$spitshell >>$Makefile <<'!NO!SUBS!'
-	rm -f libperl$(OBJ_EXT)
+	$(RMS) libperl$(OBJ_EXT)
 	mv $@ libperl$(OBJ_EXT)
 	$(AR) qv $(LIBPERL) libperl$(OBJ_EXT)
 !NO!SUBS!
@@ -921,7 +922,7 @@ $(LIBPERL): $& $(perllib_dep) $(DYNALOAD
 		;;
 	*)
 		$spitshell >>$Makefile <<'!NO!SUBS!'
-	rm -f $(LIBPERL)
+	$(RMS) $(LIBPERL)
 	$(AR) rc $(LIBPERL) $(perllib_objs) $(DYNALOADER)
 	@$(ranlib) $(LIBPERL)
 !NO!SUBS!
@@ -954,7 +955,7 @@ $(MINIPERL_EXE): lib/buildcustomize.pl
 	amigaos*)
 		$spitshell >>$Makefile <<'!NO!SUBS!'
 lib/buildcustomize.pl: $& $(miniperl_objs) write_buildcustomize.pl
-	-@rm -f miniperl.xok
+	@$(RMS) miniperl.xok
 	$(CC) $(CLDFLAGS) -o $(MINIPERL_EXE) \
 	    $(miniperl_objs) $(libs)
 #	$(LDLIBPTH) ./miniperl$(HOST_EXE_EXT) -w -Ilib -Idist/Exporter/lib -MExporter -e '<?>' || sh -c 'echo >&2 Failed to build miniperl.  Please run make minitest; exit 1'
@@ -992,7 +993,7 @@ NAMESPACEFLAGS = -force_flat_namespace
 		esac
 		$spitshell >>$Makefile <<'!NO!SUBS!'
 lib/buildcustomize.pl: $& $(miniperl_objs) write_buildcustomize.pl
-	-@rm -f miniperl.xok
+	@$(RMS) miniperl.xok
 	$(CC) $(CLDFLAGS) $(NAMESPACEFLAGS) -o $(MINIPERL_EXE) \
 	    $(miniperl_objs) $(libs)
 	$(LDLIBPTH) ./miniperl$(HOST_EXE_EXT) -w -Ilib -Idist/Exporter/lib -MExporter -e '<?>' || sh -c 'echo >&2 Failed to build miniperl.  Please run make minitest; exit 1'
@@ -1003,8 +1004,8 @@ lib/buildcustomize.pl: $& $(miniperl_obj
 		if test "X$hostperl" != X; then
 			$spitshell >>$Makefile <<!GROK!THIS!
 lib/buildcustomize.pl: \$& \$(miniperl_dep) write_buildcustomize.pl
-	-@rm -f miniperl.xok
-	-@rm \$(MINIPERL_EXE)
+	@\$(RMS) miniperl.xok
+	@\$(RMS) \$(MINIPERL_EXE)
 	\$(LNS) \$(HOST_PERL) \$(MINIPERL_EXE)
 	\$(LDLIBPTH) ./miniperl\$(HOST_EXE_EXT) -w -Ilib -Idist/Exporter/lib -MExporter -e '<?>' || sh -c 'echo >&2 Failed to build miniperl.  Please run make minitest; exit 1'
 	\$(MINIPERL) -f write_buildcustomize.pl 'osname' "$osname"
@@ -1012,7 +1013,7 @@ lib/buildcustomize.pl: \$& \$(miniperl_d
 		else
 			$spitshell >>$Makefile <<'!NO!SUBS!'
 lib/buildcustomize.pl: $& $(miniperl_dep) write_buildcustomize.pl
-	-@rm -f miniperl.xok
+	@$(RMS) miniperl.xok
 	$(CC) $(CLDFLAGS) -o $(MINIPERL_EXE) \
 	    $(miniperl_objs) $(libs)
 	$(LDLIBPTH) ./miniperl$(HOST_EXE_EXT) -w -Ilib -Idist/Exporter/lib -MExporter -e '<?>' || sh -c 'echo >&2 Failed to build miniperl.  Please run make minitest; exit 1'
@@ -1025,7 +1026,7 @@ lib/buildcustomize.pl: $& $(miniperl_dep
 	$spitshell >>$Makefile <<'!NO!SUBS!'
 
 $(PERL_EXE): $& $(perlmain_dep) $(LIBPERL) $(static_ext) ext.libs $(PERLEXPORT) write_buildcustomize.pl
-	-@rm -f miniperl.xok
+	@$(RMS) miniperl.xok
 !NO!SUBS!
 
         case $osname in
@@ -1119,8 +1120,8 @@ pod/perl5281delta.pod: pod/perldelta.pod
 	$(LNS) perldelta.pod pod/perl5281delta.pod
 
 extra.pods: $(MINIPERL_EXE)
-	-@test ! -f extra.pods || rm -f `cat extra.pods`
-	-@rm -f extra.pods
+	-@test ! -f extra.pods || $(RMS) `cat extra.pods`
+	@$(RMS) extra.pods
 	-@for x in `grep -l '^=[a-z]' README.* | grep -v README.vms` ; do \
 	    nx=`echo $$x | sed -e "s/README\.//"`; \
 	    $(LNS) ../$$x "pod/perl"$$nx".pod" ; \
@@ -1340,11 +1341,11 @@ realclean:	_realcleaner _mopup
 	@echo "Note that '$(MAKE) realclean' does not delete config.sh or Policy.sh"
 
 _clobber:
-	-@rm -f Cross/run-* Cross/to-* Cross/from-* Cross/mkdir
-	-rm -rf host
-	rm -f t/test_state
-	rm -f config.sh cppstdin Policy.sh extras.lst
-	rm -f $(MANIFEST_SRT)
+	@$(RMS) Cross/run-* Cross/to-* Cross/from-* Cross/mkdir
+	$(RMS_R) host
+	$(RMS) t/test_state
+	$(RMS) config.sh cppstdin Policy.sh extras.lst
+	$(RMS) $(MANIFEST_SRT)
 
 clobber:	_realcleaner _mopup _clobber
 
@@ -1352,23 +1353,23 @@ distclean:	clobber
 
 # Like distclean but also removes emacs backups and *.orig.
 veryclean:	_verycleaner _mopup _clobber
-	-@rm -f Obsolete Wanted
+	$(RMS) Obsolete Wanted
 
 # Do not 'make _mopup' directly.
 _mopup:
-	rm -f *$(OBJ_EXT) *$(LIB_EXT) all perlmain.c opmini.c perlmini.c generate_uudmap$(EXE_EXT) $(generated_headers)
+	$(RMS) *$(OBJ_EXT) *$(LIB_EXT) all perlmain.c opmini.c perlmini.c generate_uudmap$(EXE_EXT) $(generated_headers)
 	-rmdir .depending
-	-@test -f extra.pods && rm -f `cat extra.pods`
-	-@test -f vms/README_vms.pod && rm -f vms/README_vms.pod
-	-rm -f perl.exp ext.libs $(generated_pods) uni.data opmini.o perlmini.o pod/roffitall
-	-rm -f perl.export perl.dll perl.libexp perl.map perl.def
-	-rm -f *perl.xok
-	-rm -f cygwin.c libperl*.def libperl*.dll cygperl*.dll *.exe.stackdump
-	-rm -f $(PERL_EXE) $(MINIPERL_EXE) $(LIBPERL) libperl.* microperl
-	-rm -f config.arch config.over $(DTRACE_H)
+	-@test -f extra.pods && $(RMS) `cat extra.pods`
+	-@test -f vms/README_vms.pod && $(RMS) vms/README_vms.pod
+	$(RMS) perl.exp ext.libs $(generated_pods) uni.data opmini.o perlmini.o pod/roffitall
+	$(RMS) perl.export perl.dll perl.libexp perl.map perl.def
+	$(RMS) *perl.xok
+	$(RMS) cygwin.c libperl*.def libperl*.dll cygperl*.dll *.exe.stackdump
+	$(RMS) $(PERL_EXE) $(MINIPERL_EXE) $(LIBPERL) libperl.* microperl
+	$(RMS) config.arch config.over $(DTRACE_H)
 
 _cleaner1:
-	-cd os2; rm -f Makefile
+	-cd os2; $(RMS) Makefile
 	-cd pod; $(LDLIBPTH) $(MAKE) $(CLEAN)
 	-cd utils; $(LDLIBPTH) $(MAKE) $(CLEAN)
 	-@if test -f $(MINIPERL_EXE) ; then \
@@ -1378,31 +1379,31 @@ _cleaner1:
 	else \
 	sh $(CLEAN).sh ; \
 	fi
-	rm -f realclean.sh veryclean.sh
-	-rm -f `grep -v ^# mkppport.lst | grep . | sed -e 's/$$/\/ppport.h/'`
+	$(RMS) realclean.sh veryclean.sh
+	$(RMS) `grep -v ^# mkppport.lst | grep . | sed -e 's/$$/\/ppport.h/'`
 
 # Dear POSIX, thanks for making the default to xargs to be
 # run once if nothhing is passed in. It is such a great help.
 
 # Some systems do not support "?", so keep these files separate.
 _cleaner2:
-	-rm -f core.*perl.*.? t/core.perl.*.? .?*.c
-	rm -f core *perl.core t/core t/*perl.core core.* t/core.*
-	rm -f t/$(PERL_EXE) t/rantests
-	rm -rf t/tmp*
-	rm -rf $(addedbyconf)
-	rm -f $(FIRSTMAKEFILE) $(FIRSTMAKEFILE).old makefile.old utils/Makefile
-	rm -f $(private)
-	rm -rf $(unidatafiles) $(unidatadirs)
-	rm -rf lib/auto
-	rm -f lib/.exists lib/*/.exists lib/*/*/.exists
-	rm -f h2ph.man
-	rm -rf .config
-	rm -f preload
-	rm -f pod2htmd.tmp
-	rm -rf pod/perlfunc pod/perlipc
+	$(RMS) core.*perl.*.? t/core.perl.*.? .?*.c
+	$(RMS) core *perl.core t/core t/*perl.core core.* t/core.*
+	$(RMS) t/$(PERL_EXE) t/rantests
+	$(RMS_R) t/tmp*
+	$(RMS_R) $(addedbyconf)
+	$(RMS) $(FIRSTMAKEFILE) $(FIRSTMAKEFILE).old makefile.old utils/Makefile
+	$(RMS) $(private)
+	$(RMS_R) $(unidatafiles) $(unidatadirs)
+	$(RMS_R) lib/auto
+	$(RMS) lib/.exists lib/*/.exists lib/*/*/.exists
+	$(RMS) h2ph.man
+	$(RMS_R) .config
+	$(RMS) preload
+	$(RMS) pod2htmd.tmp
+	$(RMS_R) pod/perlfunc pod/perlipc
 	-rmdir ext/B/lib
-	rm -f so_locations $(LIBPERL_NONSHR) $(MINIPERL_NONSHR)
+	$(RMS) so_locations $(LIBPERL_NONSHR) $(MINIPERL_NONSHR)
 	-rmdir lib/version lib/threads lib/inc/ExtUtils lib/inc lib/encoding
 	-rmdir lib/autodie/exception lib/autodie/Scope lib/autodie lib/XS
 	-rmdir lib/Win32API lib/VMS lib/Unicode/Collate/Locale
@@ -1452,11 +1453,11 @@ _realcleaner:
 _verycleaner:
 	@$(LDLIBPTH) $(MAKE) _cleaner1 CLEAN=veryclean
 	@$(LDLIBPTH) $(MAKE) _cleaner2
-	-rm -f *~ *.orig */*~ */*.orig */*/*~ */*/*.orig
+	$(RMS) *~ *.orig */*~ */*.orig */*/*~ */*/*.orig
 
 .PHONY: lint
 lint: $(c)
-	rm -f *.ln
+	$(RMS) *.ln
 	lint $(lintflags) -DPERL_CORE -D_REENTRANT -DDEBUGGING -D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64 $(c)
 
 cscopeflags = -Rb  # Recursive, build-only.
@@ -1514,7 +1515,7 @@ case "$targethost" in
 '') $spitshell >>$Makefile <<'!NO!SUBS!'
 test_prep test-prep: test_prep_pre $(MINIPERL_EXE) $(unidatafiles) $(PERL_EXE) \
 	$(dynamic_ext) $(TEST_PERL_DLL) runtests $(generated_pods) common_build
-	cd t && (rm -f $(PERL_EXE); $(LNS) ../$(PERL_EXE) $(PERL_EXE))
+	cd t && ($(RMS) $(PERL_EXE); $(LNS) ../$(PERL_EXE) $(PERL_EXE))
 
 !NO!SUBS!
 ;;
@@ -1564,7 +1565,7 @@ test_prep test-prep: test_prep_pre \$(MI
 	$to config.sh
 # --- For lib/diagnostics.t with -Duseshrplib
 	$to \$(PERL_EXE)
-	cd t && (rm -f \$(PERL_EXE); \$(LNS) ../\$(PERL_EXE) \$(PERL_EXE)) && cd ..
+	cd t && (\$(RMS) \$(PERL_EXE); \$(LNS) ../\$(PERL_EXE) \$(PERL_EXE)) && cd ..
 	$to t/\$(PERL_EXE)
 
 !GROK!THIS!
@@ -1574,7 +1575,7 @@ esac
 $spitshell >>$Makefile <<'!NO!SUBS!'
 test_prep_reonly: $(MINIPERL_EXE) $(PERL_EXE) $(dynamic_ext_re) $(TEST_PERL_DLL)
 	$(MINIPERL) make_ext.pl $(dynamic_ext_re) MAKE="$(MAKE)" LIBPERL_A=$(LIBPERL) LINKTYPE=dynamic
-	cd t && (rm -f $(PERL_EXE); $(LNS) ../$(PERL_EXE) $(PERL_EXE))
+	cd t && ($(RMS) $(PERL_EXE); $(LNS) ../$(PERL_EXE) $(PERL_EXE))
 !NO!SUBS!
 
 case "$targethost" in
@@ -1629,7 +1630,7 @@ minitest_prep:
 	@echo "You may see some irrelevant test failures if you have been unable"
 	@echo "to build lib/Config.pm, or the Unicode data files."
 	@echo " "
-	- cd t && (rm -f $(PERL_EXE); $(LNS) ../$(MINIPERL_EXE) $(PERL_EXE))
+	- cd t && ($(RMS) $(PERL_EXE); $(LNS) ../$(MINIPERL_EXE) $(PERL_EXE))
 
 MINITEST_TESTS = base/*.t comp/*.t cmd/*.t run/*.t io/*.t re/*.t opbasic/*.t op/*.t uni/*.t perf/*.t
 
