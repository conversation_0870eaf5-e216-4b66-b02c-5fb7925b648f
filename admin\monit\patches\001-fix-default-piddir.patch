--- a/configure
+++ b/configure
@@ -14436,14 +14436,7 @@ fi
 # Find the right directory to put the root-mode PID file in
 { $as_echo "$as_me:${as_lineno-$LINENO}: checking pid file location" >&5
 $as_echo_n "checking pid file location... " >&6; }
-if test -d "/run"
-then
-	piddir="/run"
-elif test -d "/var/run"; then
-        piddir="/var/run"
-elif test -d "/etc"; then
-        piddir="/etc"
-fi
+piddir="/var/run"
 
 
 cat >>confdefs.h <<_ACEOF
--- a/configure.ac
+++ b/configure.ac
@@ -501,14 +501,7 @@ fi
 
 # Find the right directory to put the root-mode PID file in
 AC_MSG_CHECKING([pid file location])
-if test -d "/run"
-then
-	piddir="/run"
-elif test -d "/var/run"; then
-        piddir="/var/run"
-elif test -d "/etc"; then
-        piddir="/etc"
-fi
+piddir="/var/run"
 
 AC_DEFINE_UNQUOTED([PIDDIR], "$piddir",
 	  [Define to the pid storage directory.])
