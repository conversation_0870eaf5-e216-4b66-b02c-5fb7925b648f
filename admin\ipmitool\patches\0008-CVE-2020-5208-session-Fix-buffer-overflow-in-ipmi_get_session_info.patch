From 4f7778ed232a92bde388f38917b94f458a82c78e Mon Sep 17 00:00:00 2001
From: Chrostoper Ertl <<EMAIL>>
Date: Thu, 28 Nov 2019 16:51:49 +0000
Subject: [PATCH 08/11] session: Fix buffer overflow in ipmi_get_session_info

Partial fix for CVE-2020-5208, see
https://github.com/ipmitool/ipmitool/security/advisories/GHSA-g659-9qxw-p7cp

The `ipmi_get_session_info` function does not properly check the
response `data_len`, which is used as a copy size, allowing stack buffer
overflow.
---
 lib/ipmi_session.c | 12 ++++++++----
 1 file changed, 8 insertions(+), 4 deletions(-)

--- a/lib/ipmi_session.c
+++ b/lib/ipmi_session.c
@@ -309,8 +309,10 @@ ipmi_get_session_info(struct ipmi_intf
 		}
 		else
 		{
-			memcpy(&session_info,  rsp->data, rsp->data_len);
-			print_session_info(&session_info, rsp->data_len);
+			memcpy(&session_info,  rsp->data,
+			       __min(rsp->data_len, sizeof(session_info)));
+			print_session_info(&session_info,
+			                   __min(rsp->data_len, sizeof(session_info)));
 		}
 		break;
 		
@@ -341,8 +343,10 @@ ipmi_get_session_info(struct ipmi_intf
 				break;
 			}
 
-			memcpy(&session_info,  rsp->data, rsp->data_len);
-			print_session_info(&session_info, rsp->data_len);
+			memcpy(&session_info,  rsp->data,
+			       __min(rsp->data_len, sizeof(session_info)));
+			print_session_info(&session_info,
+			                   __min(rsp->data_len, sizeof(session_info)));
 			
 		} while (i <= session_info.session_slot_count);
 		break;
