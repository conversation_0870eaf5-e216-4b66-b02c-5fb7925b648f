commit 31285a20390a5e53a74a2a71d1b5c82f366ddd5a
Author: <PERSON> <<EMAIL>>
Date:   Tue May 6 11:49:05 2014 +0000

    gcc: revert an upstream patch that is causing a regression on powerpc
    
    https://forum.openwrt.org/viewtopic.php?pid=232494#p232494
    
    Signed-off-by: <PERSON> <<EMAIL>>
    
    SVN-Revision: 40709

Revert of:

commit 275035b56823b26d5fb7e90fad945b998648edf2
Author: bergner <bergner@138bc75d-0d04-0410-961f-82ee72b054a4>
Date:   Thu Sep 5 14:09:07 2013 +0000

        PR target/58139
        * reginfo.c (choose_hard_reg_mode): Scan through all mode classes
        looking for widest mode.


    git-svn-id: svn+ssh://gcc.gnu.org/svn/gcc/trunk@202286 138bc75d-0d04-0410-961f-82ee72b054a4


--- a/gcc/reginfo.c
+++ b/gcc/reginfo.c
@@ -637,35 +637,40 @@ choose_hard_reg_mode (unsigned int regno
        mode = GET_MODE_WIDER_MODE (mode))
     if ((unsigned) hard_regno_nregs[regno][mode] == nregs
 	&& HARD_REGNO_MODE_OK (regno, mode)
-	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode))
-	&& GET_MODE_SIZE (mode) > GET_MODE_SIZE (found_mode))
+	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode)))
       found_mode = mode;
 
+  if (found_mode != VOIDmode)
+    return found_mode;
+
   for (mode = GET_CLASS_NARROWEST_MODE (MODE_FLOAT);
        mode != VOIDmode;
        mode = GET_MODE_WIDER_MODE (mode))
     if ((unsigned) hard_regno_nregs[regno][mode] == nregs
 	&& HARD_REGNO_MODE_OK (regno, mode)
-	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode))
-	&& GET_MODE_SIZE (mode) > GET_MODE_SIZE (found_mode))
+	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode)))
       found_mode = mode;
 
+  if (found_mode != VOIDmode)
+    return found_mode;
+
   for (mode = GET_CLASS_NARROWEST_MODE (MODE_VECTOR_FLOAT);
        mode != VOIDmode;
        mode = GET_MODE_WIDER_MODE (mode))
     if ((unsigned) hard_regno_nregs[regno][mode] == nregs
 	&& HARD_REGNO_MODE_OK (regno, mode)
-	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode))
-	&& GET_MODE_SIZE (mode) > GET_MODE_SIZE (found_mode))
+	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode)))
       found_mode = mode;
 
+  if (found_mode != VOIDmode)
+    return found_mode;
+
   for (mode = GET_CLASS_NARROWEST_MODE (MODE_VECTOR_INT);
        mode != VOIDmode;
        mode = GET_MODE_WIDER_MODE (mode))
     if ((unsigned) hard_regno_nregs[regno][mode] == nregs
 	&& HARD_REGNO_MODE_OK (regno, mode)
-	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode))
-	&& GET_MODE_SIZE (mode) > GET_MODE_SIZE (found_mode))
+	&& (! call_saved || ! HARD_REGNO_CALL_PART_CLOBBERED (regno, mode)))
       found_mode = mode;
 
   if (found_mode != VOIDmode)
