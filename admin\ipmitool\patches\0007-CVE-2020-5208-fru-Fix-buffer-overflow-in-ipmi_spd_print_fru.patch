From 910e5782b7d9f222d4e34d3505d0d636ff757103 Mon Sep 17 00:00:00 2001
From: Chrostoper Ertl <<EMAIL>>
Date: Thu, 28 Nov 2019 16:44:18 +0000
Subject: [PATCH 07/11] fru: Fix buffer overflow in ipmi_spd_print_fru

Partial fix for CVE-2020-5208, see
https://github.com/ipmitool/ipmitool/security/advisories/GHSA-g659-9qxw-p7cp

The `ipmi_spd_print_fru` function has a similar issue as the one fixed
by the previous commit in `read_fru_area_section`. An initial request is
made to get the `fru.size`, which is used as the size for the allocation
of `spd_data`. Inside a loop, further requests are performed to get the
copy sizes which are not checked before being used as the size for a
copy into the buffer.
---
 lib/dimm_spd.c | 9 ++++++++-
 1 file changed, 8 insertions(+), 1 deletion(-)

--- a/lib/dimm_spd.c
+++ b/lib/dimm_spd.c
@@ -1621,7 +1621,7 @@ ipmi_spd_print_fru(struct ipmi_intf * in
 	struct ipmi_rq req;
 	struct fru_info fru;
 	uint8_t *spd_data, msg_data[4];
-	int len, offset;
+	uint32_t len, offset;
 
 	msg_data[0] = id;
 
@@ -1697,6 +1697,13 @@ ipmi_spd_print_fru(struct ipmi_intf * in
 		}
 
 		len = rsp->data[0];
+		if(rsp->data_len < 1
+		   || len > rsp->data_len - 1
+		   || len > fru.size - offset)
+		{
+			printf(" Not enough buffer size");
+			return -1;
+		}
 		memcpy(&spd_data[offset], rsp->data + 1, len);
 		offset += len;
 	} while (offset < fru.size);
