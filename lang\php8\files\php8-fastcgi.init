#!/bin/sh /etc/rc.common

START=50

SERVICE_DAEMONIZE=1
SERVICE_WRITE_PID=1

start_instance() {
	local section="$1"
	local enabled
	local port

	config_get_bool enabled "$section" 'enabled' 0
	config_get port "$section" 'port'

	[ $enabled -gt 0 ] || return 1

	PHP_FCGI_CHILDREN='' \
	service_start /usr/bin/php8-fcgi ${port:+-b $port}
}

start() {
	config_load 'php8-fastcgi'
	config_foreach start_instance 'php8-fastcgi'
}

stop() {
	service_stop /usr/bin/php8-fcgi
}
