--- a/mtd-rw.c
+++ b/mtd-rw.c
@@ -54,7 +54,11 @@ MODULE_PARM_DESC(i_want_a_brick, "Make a
 
 static int set_writeable(unsigned n, bool w)
 {
+#ifndef CONFIG_MTD
+	struct mtd_info *mtd = -ENOSYS;
+#else
 	struct mtd_info *mtd = get_mtd_device(NULL, n);
+#endif
 	int err;
 
 	if (IS_ERR(mtd)) {
@@ -76,7 +80,9 @@ static int set_writeable(unsigned n, boo
 		err = 0;
 	}
 
+#ifdef CONFIG_MTD
 	put_mtd_device(mtd);
+#endif
 	return err;
 }
 
