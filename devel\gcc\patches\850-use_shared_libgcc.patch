commit dcfc40358b5a3cae7320c17f8d1cebd5ad5540cd
Author: <PERSON> <<EMAIL>>
Date:   Sun Feb 12 20:25:47 2012 +0000

    gcc 4.6: port over the missing patch 850-use_shared_libgcc.patch to prevent libgcc crap from leaking into every single binary
    
    SVN-Revision: 30486
--- a/gcc/config/arm/linux-eabi.h
+++ b/gcc/config/arm/linux-eabi.h
@@ -126,10 +126,6 @@
   "%{Ofast|ffast-math|funsafe-math-optimizations:crtfastmath.o%s} "	\
   LINUX_OR_ANDROID_LD (GNU_USER_TARGET_ENDFILE_SPEC, ANDROID_ENDFILE_SPEC)
 
-/* Use the default LIBGCC_SPEC, not the version in linux-elf.h, as we
-   do not use -lfloat.  */
-#undef LIBGCC_SPEC
-
 /* Clear the instruction cache from `beg' to `end'.  This is
    implemented in lib1funcs.S, so ensure an error if this definition
    is used.  */
--- a/gcc/config/linux.h
+++ b/gcc/config/linux.h
@@ -53,6 +53,10 @@ see the files COPYING3 and COPYING.RUNTI
 	builtin_assert ("system=posix");			\
     } while (0)
 
+#ifndef LIBGCC_SPEC
+#define LIBGCC_SPEC "%{static|static-libgcc:-lgcc}%{!static:%{!static-libgcc:-lgcc_s}}"
+#endif
+
 /* Determine which dynamic linker to use depending on whether GLIBC or
    uClibc or Bionic or musl is the default C library and whether
    -muclibc or -mglibc or -mbionic or -mmusl has been passed to change
--- a/libgcc/mkmap-symver.awk
+++ b/libgcc/mkmap-symver.awk
@@ -136,5 +136,5 @@ function output(lib) {
   else if (inherit[lib])
     printf("} %s;\n", inherit[lib]);
   else
-    printf ("\n  local:\n\t*;\n};\n");
+    printf ("\n\t*;\n};\n");
 }
--- a/gcc/config/rs6000/linux.h
+++ b/gcc/config/rs6000/linux.h
@@ -60,6 +60,9 @@
 #undef	CPP_OS_DEFAULT_SPEC
 #define CPP_OS_DEFAULT_SPEC "%(cpp_os_linux)"
 
+#undef LIBGCC_SPEC
+#define LIBGCC_SPEC "%{!static:%{!static-libgcc:-lgcc_s}} -lgcc"
+
 #undef  LINK_SHLIB_SPEC
 #define LINK_SHLIB_SPEC "%{shared:-shared} %{!shared: %{static:-static}}"
 
