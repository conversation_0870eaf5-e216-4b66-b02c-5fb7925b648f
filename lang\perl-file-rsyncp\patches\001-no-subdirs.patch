--- a/Makefile.PL
+++ b/Makefile.PL
@@ -8,7 +8,7 @@ WriteMakefile(
                             Getopt::Long => 2.24,	# need OO interface
                          },
     'PMLIBDIRS'       => ['lib'],
-    'DIR'             => ['Digest', 'FileList'],
+    'DIR'             => [],
     ($] >= 5.005 ?    ## Add these new keywords supported since 5.005
       (ABSTRACT_FROM  => 'lib/File/RsyncP.pm', # retrieve abstract from module
        AUTHOR         => '<PERSON> <<EMAIL>>')
