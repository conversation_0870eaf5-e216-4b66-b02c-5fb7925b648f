From 98b47424cf548f58c4d295fa8235210406ea85ca Mon Sep 17 00:00:00 2001
From: Chrostoper Ertl <<EMAIL>>
Date: Thu, 28 Nov 2019 17:13:45 +0000
Subject: [PATCH 11/11] fru, sdr: Fix id_string buffer overflows

Final part of the fixes for CVE-2020-5208, see
https://github.com/ipmitool/ipmitool/security/advisories/GHSA-g659-9qxw-p7cp

9 variants of stack buffer overflow when parsing `id_string` field of
SDR records returned from `CMD_GET_SDR` command.

SDR record structs have an `id_code` field, and an `id_string` `char`
array.

The length of `id_string` is calculated as `(id_code & 0x1f) + 1`,
which can be larger than expected 16 characters (if `id_code = 0xff`,
then length will be `(0xff & 0x1f) + 1 = 32`).

In numerous places, this can cause stack buffer overflow when copying
into fixed buffer of size `17` bytes from this calculated length.
---
 lib/ipmi_fru.c |  2 +-
 lib/ipmi_sdr.c | 40 ++++++++++++++++++++++++----------------
 2 <USER> <GROUP>, 25 insertions(+), 17 deletions(-)

--- a/lib/ipmi_fru.c
+++ b/lib/ipmi_fru.c
@@ -3062,7 +3062,7 @@ ipmi_fru_print(struct ipmi_intf * intf,
 		return 0;
 
 	memset(desc, 0, sizeof(desc));
-	memcpy(desc, fru->id_string, fru->id_code & 0x01f);
+	memcpy(desc, fru->id_string, __min(fru->id_code & 0x01f, sizeof(desc)));
 	desc[fru->id_code & 0x01f] = 0;
 	printf("FRU Device Description : %s (ID %d)\n", desc, fru->device_id);
 
--- a/lib/ipmi_sdr.c
+++ b/lib/ipmi_sdr.c
@@ -2084,7 +2084,7 @@ ipmi_sdr_print_sensor_eventonly(struct i
 		return -1;
 
 	memset(desc, 0, sizeof (desc));
-	snprintf(desc, (sensor->id_code & 0x1f) + 1, "%s", sensor->id_string);
+	snprintf(desc, sizeof(desc), "%.*s", (sensor->id_code & 0x1f) + 1, sensor->id_string);
 
 	if (verbose) {
 		printf("Sensor ID              : %s (0x%x)\n",
@@ -2135,7 +2135,7 @@ ipmi_sdr_print_sensor_mc_locator(struct
 		return -1;
 
 	memset(desc, 0, sizeof (desc));
-	snprintf(desc, (mc->id_code & 0x1f) + 1, "%s", mc->id_string);
+	snprintf(desc, sizeof(desc), "%.*s", (mc->id_code & 0x1f) + 1, mc->id_string);
 
 	if (verbose == 0) {
 		if (csv_output)
@@ -2228,7 +2228,7 @@ ipmi_sdr_print_sensor_generic_locator(st
 	char desc[17];
 
 	memset(desc, 0, sizeof (desc));
-	snprintf(desc, (dev->id_code & 0x1f) + 1, "%s", dev->id_string);
+	snprintf(desc, sizeof(desc), "%.*s", (dev->id_code & 0x1f) + 1, dev->id_string);
 
 	if (!verbose) {
 		if (csv_output)
@@ -2285,7 +2285,7 @@ ipmi_sdr_print_sensor_fru_locator(struct
 	char desc[17];
 
 	memset(desc, 0, sizeof (desc));
-	snprintf(desc, (fru->id_code & 0x1f) + 1, "%s", fru->id_string);
+	snprintf(desc, sizeof(desc), "%.*s", (fru->id_code & 0x1f) + 1, fru->id_string);
 
 	if (!verbose) {
 		if (csv_output)
@@ -2489,35 +2489,43 @@ ipmi_sdr_print_name_from_rawentry(struct
 
    int rc =0;
    char desc[17];
+   const char *id_string;
+   uint8_t id_code;
    memset(desc, ' ', sizeof (desc));
 
    switch ( type) {
       case SDR_RECORD_TYPE_FULL_SENSOR:
       record.full = (struct sdr_record_full_sensor *) raw;
-      snprintf(desc, (record.full->id_code & 0x1f) +1, "%s",
-               (const char *)record.full->id_string);
+      id_code = record.full->id_code;
+      id_string = record.full->id_string;
       break;
+
       case SDR_RECORD_TYPE_COMPACT_SENSOR:
       record.compact = (struct sdr_record_compact_sensor *) raw	;
-      snprintf(desc, (record.compact->id_code & 0x1f)  +1, "%s",
-               (const char *)record.compact->id_string);
+      id_code = record.compact->id_code;
+      id_string = record.compact->id_string;
       break;
+
       case SDR_RECORD_TYPE_EVENTONLY_SENSOR:
       record.eventonly  = (struct sdr_record_eventonly_sensor *) raw ;
-      snprintf(desc, (record.eventonly->id_code & 0x1f)  +1, "%s",
-               (const char *)record.eventonly->id_string);
-      break;            
+      id_code = record.eventonly->id_code;
+      id_string = record.eventonly->id_string;
+      break;
+
       case SDR_RECORD_TYPE_MC_DEVICE_LOCATOR:
       record.mcloc  = (struct sdr_record_mc_locator *) raw ;
-      snprintf(desc, (record.mcloc->id_code & 0x1f)  +1, "%s",
-               (const char *)record.mcloc->id_string);		
+      id_code = record.mcloc->id_code;
+      id_string = record.mcloc->id_string;
       break;
+
       default:
       rc = -1;
-      break;
-   }   
+   }
+   if (!rc) {
+       snprintf(desc, sizeof(desc), "%.*s", (id_code & 0x1f) + 1, id_string);
+   }
 
-      lprintf(LOG_INFO, "ID: 0x%04x , NAME: %-16s", id, desc);
+   lprintf(LOG_INFO, "ID: 0x%04x , NAME: %-16s", id, desc);
    return rc;
 }
 
