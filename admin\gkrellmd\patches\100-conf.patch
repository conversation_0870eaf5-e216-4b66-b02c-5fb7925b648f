--- a/server/gkrellmd.conf
+++ b/server/gkrellmd.conf
@@ -4,7 +4,7 @@
 # the client update frequency.  Values may be from 1 to 10 and should be
 # smaller values to reduce network traffic.
 #
-#update-hz 3
+update-hz 10
 
 # Limit number of simultaneous clients allowed to connect.
 #
@@ -30,18 +30,18 @@
 # Drop privileges after startup (you must start gkrellmd as root to do it).
 # NOTE: Option ignored on Windows
 #
-#user	nobody
+user	nobody
 #group	proc
 
 # Create a PID file for the running gkrellmd.  Default is no PID file.
 # NOTE: Option ignored on Windows
 #
-#pidfile /var/run/gkrellmd.pid
+pidfile /var/run/gkrellmd.pid
 
 # Run in background and detach from the controlling terminal
 # NOTE: Option ignored on Windows
 #
-#detach
+detach
 
 # Enable writing logging message to the system syslog file
 # NOTE: On windows this enables logging to the windows event log
