--- a/lib/crypto/c_src/engine.c
+++ b/lib/crypto/c_src/engine.c
@@ -239,7 +239,7 @@ ERL_NIF_TERM engine_load_dynamic_nif(Erl
 #ifdef HAS_ENGINE_SUPPORT
     ASSERT(argc == 0);
 
-    ENGINE_load_dynamic();
+    ENGINE_load_builtin_engines();
     return atom_ok;
 #else
     return atom_notsup;
--- a/lib/crypto/c_src/info.c
+++ b/lib/crypto/c_src/info.c
@@ -46,6 +46,11 @@
 #endif
 
 
+#if OPENSSL_VERSION_NUMBER < PACKED_OPENSSL_VERSION_PLAIN(1,1,0)
+#define OPENSSL_VERSION	SSLEAY_VERSION
+#define OpenSSL_version	SSLeay_version
+#endif
+
 #ifdef HAVE_DYNAMIC_CRYPTO_LIB
 
 char *crypto_callback_name = CB_NAME;
@@ -132,7 +137,7 @@ ERL_NIF_TERM info_lib(ErlNifEnv *env, in
     ASSERT(argc == 0);
 
     name_sz = strlen(libname);
-    ver = SSLeay_version(SSLEAY_VERSION);
+    ver = OpenSSL_version(OPENSSL_VERSION);
     ver_sz = strlen(ver);
     ver_num = OPENSSL_VERSION_NUMBER;
 
--- a/lib/crypto/c_src/otp_test_engine.c
+++ b/lib/crypto/c_src/otp_test_engine.c
@@ -101,9 +101,11 @@ static int test_init(ENGINE *e) {
         goto err;
 #endif /* if defined(FAKE_RSA_IMPL) */
 
+#if OPENSSL_VERSION_NUMBER < PACKED_OPENSSL_VERSION_PLAIN(1,1,0)
     /* Load all digest and cipher algorithms. Needed for password protected private keys */
     OpenSSL_add_all_ciphers();
     OpenSSL_add_all_digests();
+#endif
 
     return 111;
 
