--- a/tools/icu/icu-generic.gyp
+++ b/tools/icu/icu-generic.gyp
@@ -107,6 +107,7 @@
           'sources': [
             '<@(icu_src_i18n)'
           ],
+          'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
           'include_dirs': [
             '<(icu_path)/source/i18n',
           ],
@@ -115,6 +116,7 @@
           ],
           'dependencies': [ 'icuucx', 'icu_implementation', 'icu_uconfig', 'icu_uconfig_target' ],
           'direct_dependent_settings': {
+            'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
             'include_dirs': [
               '<(icu_path)/source/i18n',
             ],
@@ -201,6 +203,7 @@
               # full data - no trim needed
               'sources': [ '<(SHARED_INTERMEDIATE_DIR)/icudt<(icu_ver_major)_dat.<(icu_asm_ext)' ],
               'dependencies': [ 'genccode#host', 'icupkg#host', 'icu_implementation#host', 'icu_uconfig' ],
+              'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
               'include_dirs': [
                 '<(icu_path)/source/common',
               ],
@@ -285,6 +288,7 @@
               # This file contains the small ICU data
               'sources': [ '<(SHARED_INTERMEDIATE_DIR)/icusmdt<(icu_ver_major)_dat.<(icu_asm_ext)' ],
               # for umachine.h
+              'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
               'include_dirs': [
                 '<(icu_path)/source/common',
               ],
@@ -301,6 +305,7 @@
       'sources': [
         '<@(icu_src_stubdata)'
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
       ],
@@ -340,6 +345,7 @@
           '_XOPEN_SOURCE_EXTENDED=0',
         ]}],
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
       ],
@@ -349,6 +355,7 @@
       'cflags_c': ['-std=c99'],
       'export_dependent_settings': [ 'icu_uconfig', 'icu_uconfig_target' ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(icu_path)/source/common',
         ],
@@ -379,6 +386,7 @@
         '<(icu_path)/source/tools/toolutil/dbgutil.cpp',
         '<(icu_path)/source/tools/toolutil/dbgutil.h',
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
         '<(icu_path)/source/i18n',
@@ -398,6 +406,7 @@
         }]
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(icu_path)/source/common',
           '<(icu_path)/source/i18n',
