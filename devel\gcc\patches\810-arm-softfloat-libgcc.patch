commit 8570c4be394cff7282f332f97da2ff569a927ddb
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Wed Feb 2 20:06:12 2011 +0000

    fixup arm soft-float symbols
    
    SVN-Revision: 25325

--- a/libgcc/config/arm/t-linux
+++ b/libgcc/config/arm/t-linux
@@ -1,6 +1,10 @@
 LIB1ASMSRC = arm/lib1funcs.S
 LIB1ASMFUNCS = _udivsi3 _divsi3 _umodsi3 _modsi3 _dvmd_lnx _clzsi2 _clzdi2 \
-	_ctzsi2 _arm_addsubdf3 _arm_addsubsf3
+	_ctzsi2 _arm_addsubdf3 _arm_addsubsf3 \
+	_arm_negdf2 _arm_muldivdf3 _arm_cmpdf2 _arm_unorddf2 \
+	_arm_fixdfsi _arm_fixunsdfsi _arm_truncdfsf2 \
+	_arm_negsf2 _arm_muldivsf3 _arm_cmpsf2 _arm_unordsf2 \
+	_arm_fixsfsi _arm_fixunssfsi
 
 # Just for these, we omit the frame pointer since it makes such a big
 # difference.
--- a/gcc/config/arm/linux-elf.h
+++ b/gcc/config/arm/linux-elf.h
@@ -58,8 +58,6 @@
    %{shared:-lc} \
    %{!shared:%{profile:-lc_p}%{!profile:-lc}}"
 
-#define LIBGCC_SPEC "%{mfloat-abi=soft*:-lfloat} -lgcc"
-
 #define GLIBC_DYNAMIC_LINKER "/lib/ld-linux.so.2"
 
 #define LINUX_TARGET_LINK_SPEC  "%{h*} \
