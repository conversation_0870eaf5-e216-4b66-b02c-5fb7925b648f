#!/bin/sh /etc/rc.common

START=99
USE_PROCD=1

APPBINARY=/usr/sbin/netdata
CONFIGFILE=/etc/netdata/netdata.conf

start_service() {
	mkdir -m 0755 -p /var/cache/netdata
	chown nobody /var/cache/netdata
	mkdir -m 0755 -p /var/lib/netdata
	chown nobody /var/lib/netdata
	mkdir -m 0755 -p /var/log/netdata
	chown nobody /var/log/netdata
	procd_open_instance
	procd_set_param command $APPBINARY -D -c $CONFIGFILE
	procd_set_param file $CONFIGFILE
	procd_set_param respawn
	procd_close_instance
}
