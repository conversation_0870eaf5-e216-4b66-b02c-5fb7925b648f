--- a/deps/zlib/zlib.gyp
+++ b/deps/zlib/zlib.gyp
@@ -43,10 +43,12 @@
             'zutil.c',
             'zutil.h',
           ],
+          'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
           'include_dirs': [
             '.',
           ],
           'direct_dependent_settings': {
+            'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
             'include_dirs': [
               '.',
             ],
--- a/tools/v8_gypfiles/v8.gyp
+++ b/tools/v8_gypfiles/v8.gyp
@@ -60,6 +60,7 @@
       ],
       'hard_dependency': 1,
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(SHARED_INTERMEDIATE_DIR)',
         ],
@@ -181,6 +182,7 @@
           '<@(torque_outputs_cc)',
           '<@(torque_outputs_inc)',
         ],
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(SHARED_INTERMEDIATE_DIR)',
         ],
@@ -202,6 +204,7 @@
         'sources': [
           '<(generate_bytecode_builtins_list_output)',
         ],
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(generate_bytecode_output_root)',
           '<(SHARED_INTERMEDIATE_DIR)',
@@ -249,9 +252,11 @@
         'v8_base_without_compiler',
         'v8_shared_internal_headers',
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(SHARED_INTERMEDIATE_DIR)',
         '<(generate_bytecode_output_root)',
+        '<!@(echo "$STAGING_DIR"/usr/../usr/include)',
       ],
       'sources': [
         '<!@pymod_do_main(GN-scraper "<(V8_ROOT)/BUILD.gn"  "\\"v8_initializers.*?sources = ")',
@@ -754,6 +759,7 @@
       ],
       'includes': ['inspector.gypi'],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(generate_bytecode_output_root)',
           '<(SHARED_INTERMEDIATE_DIR)',
@@ -1353,6 +1359,7 @@
         }],
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(V8_ROOT)/include',
         ],
@@ -1701,6 +1708,7 @@
          }],
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(V8_ROOT)/include',
         ],
@@ -1881,15 +1889,19 @@
         }],
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(V8_ROOT)/third_party/zlib',
           '<(V8_ROOT)/third_party/zlib/google',
+          '<!@(echo "$STAGING_DIR"/usr/../usr/include)',
         ],
       },
       'defines': [ 'ZLIB_IMPLEMENTATION' ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(V8_ROOT)/third_party/zlib',
         '<(V8_ROOT)/third_party/zlib/google',
+        '<!@(echo "$STAGING_DIR"/usr/../usr/include)',
       ],
       'sources': [
         '<(V8_ROOT)/third_party/zlib/adler32.c',
