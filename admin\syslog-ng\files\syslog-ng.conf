#############################################################################
# OpenWrt syslog-ng.conf specific file
# which collects all local logs into a single file called /var/log/messages.
# More details about these settings can be found here:
# https://www.syslog-ng.com/technical-documents/list/syslog-ng-open-source-edition

@version: 4.0
@include "scl.conf"

options {
	chain_hostnames(no); # Enable or disable the chained hostname format.
	create_dirs(yes);
	keep_hostname(yes); # Enable or disable hostname rewriting.
	log_fifo_size(256); # The number of messages that the output queue can store.
	log_msg_size(1024); # Maximum length of a message in bytes.
	stats_freq(0); # The period between two STATS messages (sent by syslog-ng, containing statistics about dropped logs) in seconds.
	flush_lines(0); # How many lines are flushed to a destination at a time.
	use_fqdn(no); # Add Fully Qualified Domain Name instead of short hostname.
};

# syslog-ng gets messages from syslog-ng (internal) and from /dev/log

source src {
	internal();
	unix-dgram("/dev/log");
};

source net {
	network_localhost();
};

source s_network {
	default-network-drivers(
		# NOTE: TLS support
		#
		# the default-network-drivers() source driver opens the TLS
		# enabled ports as well, however without an actual key/cert
		# pair they will not operate and syslog-ng would display a
		# warning at startup.
		#
		#tls(key-file("/path/to/ssl-private-key") cert-file("/path/to/ssl-cert"))
	);
};

source kernel {
        file("/proc/kmsg" program_override("kernel"));
};

destination messages {
	file("/var/log/messages");
};

log {
	source(src);
	source(net);
        source(kernel);
	destination(messages);

	# uncomment this line to open port 514 to receive messages
	#source(s_network);
};

#
# Finally, include any user settings last so that s/he can override or
# supplement all "canned" settings inherited from the distribution.
#
@include "/etc/syslog-ng.d/" # Put any customization files in this directory
