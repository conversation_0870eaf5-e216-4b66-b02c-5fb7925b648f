#!/bin/sh /etc/rc.common

START=98
USE_PROCD=1

start_service() {
	[ -d /usr/share/homebridge ] || {
		mkdir -m 0755 -p /usr/share/homebridge
		chmod 0700 /usr/share/homebridge
		chown homebridge:homebridge /usr/share/homebridge
	}
	procd_open_instance
	procd_set_param command /usr/bin/homebridge -U /usr/share/homebridge
	procd_set_param user homebridge
	procd_set_param stdout 1
	procd_set_param stderr 1
	procd_close_instance
}
