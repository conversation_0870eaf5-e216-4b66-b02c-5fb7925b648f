--- a/deps/v8/src/compiler/backend/mips/code-generator-mips.cc
+++ b/deps/v8/src/compiler/backend/mips/code-generator-mips.cc
@@ -4101,7 +4101,7 @@ void CodeGenerator::AssembleReturn(Instr
     } else if (FLAG_debug_code) {
       __ Assert(eq, AbortReason::kUnexpectedAdditionalPopValue,
                 g.<PERSON>(additional_pop_count),
-                Operand(static_cast<int64_t>(0)));
+                Operand(static_cast<int32_t>(0)));
     }
   }
   // Functions with JS linkage have at least one parameter (the receiver).
