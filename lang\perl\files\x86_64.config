owrt:arch=x86_64
owrt:bits=64
owrt:endian=little

ccsymbols=''
cppccsymbols=''
cppsymbols='__ELF__=1 _LP64=1 __FLT_EVAL_METHOD__=0 __FXSR__=1 __GCC_ATOMIC_LLONG_LOCK_FREE=2 __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 __INT64_MAX__=9223372036854775807L __INT64_TYPE__=long\ int __INTMAX_MAX__=9223372036854775807L __INTMAX_TYPE__=long\ int __INTPTR_MAX__=9223372036854775807L __INTPTR_TYPE__=long\ int __INT_FAST16_MAX__=9223372036854775807L __INT_FAST16_TYPE__=long\ int __INT_FAST32_MAX__=9223372036854775807L __INT_FAST32_TYPE__=long\ int __INT_FAST64_MAX__=9223372036854775807L __INT_FAST64_TYPE__=long\ int __INT_LEAST64_MAX__=9223372036854775807L __INT_LEAST64_TYPE__=long\ int __LONG_MAX__=9223372036854775807L __LP64__=1 __MMX__=1 __PTRDIFF_MAX__=9223372036854775807L __PTRDIFF_TYPE__=long\ int __SIZEOF_INT128__=16 __SIZEOF_LONG_DOUBLE__=16 __SIZEOF_LONG__=8 __SIZEOF_POINTER__=8 __SIZEOF_PTRDIFF_T__=8 __SIZEOF_SIZE_T__=8 __SIZE_MAX__=18446744073709551615UL __SIZE_TYPE__=long\ unsigned\ int __SSE2_MATH__=1 __SSE2__=1 __SSE_MATH__=1 __SSE__=1 __UINT64_MAX__=18446744073709551615UL __UINT64_TYPE__=long\ unsigned\ int __UINTMAX_MAX__=18446744073709551615UL __UINTMAX_TYPE__=long\ unsigned\ int __UINTPTR_MAX__=18446744073709551615UL __UINTPTR_TYPE__=long\ unsigned\ int __UINT_FAST16_MAX__=18446744073709551615UL __UINT_FAST16_TYPE__=long\ unsigned\ int __UINT_FAST32_MAX__=18446744073709551615UL __UINT_FAST32_TYPE__=long\ unsigned\ int __UINT_FAST64_MAX__=18446744073709551615UL __UINT_FAST64_TYPE__=long\ unsigned\ int __UINT_LEAST64_MAX__=18446744073709551615UL __UINT_LEAST64_TYPE__=long\ unsigned\ int __WCHAR_MAX__=2147483647 __WCHAR_MIN__=(-2147483647\ -\ 1) __WCHAR_TYPE__=int __amd64=1 __amd64__=1 __code_model_small__=1 __k8=1 __k8__=1 __x86_64=1 __x86_64__=1 _FILE_OFFSET_BITS=64 __GLIBC__=2 __GLIBC_MINOR__=2 __GNUC__=3 __GNUC_MINOR__=4 __GNU_LIBRARY__=6 _LARGEFILE_SOURCE=1 _POSIX_C_SOURCE=199506L _POSIX_SOURCE=1 __STDC__=1 __USE_BSD=1 __USE_FILE_OFFSET64=1 __USE_LARGEFILE=1 __USE_MISC=1 __USE_POSIX=1 __USE_POSIX199309=1 __USE_POSIX199506=1 __USE_POSIX2=1 __USE_SVID=1 linux=1 __linux=1 __linux__=1 unix=1 __unix=1 __unix__=1'
d_casti32='undef'
d_double_style_ieee='define'
d_long_double_style_ieee='define'
d_long_double_style_ieee_extended='define'
d_modflproto='define'
doublekind='3'
fpossize='16'
longdblkind='3'
need_va_copy='define'
quadkind='2'

sPRIEUldbl='"LE"'
sPRIFUldbl='"LF"'
sPRIGUldbl='"LG"'
sPRIXU64='"lX"'
sPRIeldbl='"Le"'
sPRIfldbl='"Lf"'
sPRIgldbl='"Lg"'
sSCNfldbl='"Lf"'

owrt:sig_count='64'
owrt:sigs='ZERO HUP INT QUIT ILL TRAP ABRT BUS FPE KILL USR1 SEGV USR2 PIPE ALRM TERM STKFLT CHLD CONT STOP TSTP TTIN TTOU URG XCPU XFSZ VTALRM PROF WINCH IO PWR SYS'
owrt:sig_name_extra='IOT CLD POLL UNUSED'
owrt:sig_num_extra='6 17 29 31'
