--- a/configure.py
+++ b/configure.py
@@ -1288,6 +1288,15 @@ def configure_v8(o):
     options.build_v8_with_gn = FetchDeps(v8_path)
   o['variables']['build_v8_with_gn'] = b(options.build_v8_with_gn)
 
+def configure_v8_deprecated(o):
+  target_arch = options.dest_cpu
+  if target_arch == 'x86':
+    target_arch = 'ia32'
+  if target_arch == 'x86_64':
+    target_arch = 'x64'
+
+  if target_arch in ('mips', 'mips64'):
+    o['variables']['v8_use_snapshot'] = 'false'
 
 def configure_openssl(o):
   variables = o['variables']
@@ -1725,6 +1734,7 @@ configure_openssl(output)
 configure_intl(output)
 configure_static(output)
 configure_inspector(output)
+configure_v8_deprecated(output)
 
 # Forward OSS-Fuzz settings
 output['variables']['ossfuzz'] = b(options.ossfuzz)
