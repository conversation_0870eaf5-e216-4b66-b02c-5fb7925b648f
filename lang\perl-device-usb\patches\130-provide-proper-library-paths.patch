--- a/lib/Device/USB.pm
+++ b/lib/Device/USB.pm
@@ -8,8 +8,8 @@ use Carp;
 use Inline (
         C => "DATA",
         ($ENV{LIBUSB_LIBDIR}
-            ? ( LIBS => "-L\"$ENV{LIBUSB_LIBDIR}\" " .
-                        ($^O eq 'MSWin32' ? ' -llibusb -L\"$ENV{WINDDK}\\lib\\crt\\i386\" -lmsvcrt ' : '-lusb-0.1') )
+            ? ( LIBS => "-L$ENV{LIBUSB_LIBDIR} " .
+                        ($^O eq 'MSWin32' ? ' -llibusb -L$ENV{WINDDK}\\lib\\crt\\i386 -lmsvcrt ' : '-lusb-0.1') )
             : ( LIBS => '-lusb-0.1', )
         ),
         ($ENV{LIBUSB_INCDIR} ? ( INC => "-I\"$ENV{LIBUSB_INCDIR}\"" ) : () ),
