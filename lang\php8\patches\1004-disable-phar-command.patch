--- a/ext/phar/config.m4
+++ b/ext/phar/config.m4
@@ -19,7 +19,7 @@ if test "$PHP_PHAR" != "no"; then
   fi
   PHP_ADD_EXTENSION_DEP(phar, hash, true)
   PHP_ADD_EXTENSION_DEP(phar, spl, true)
-  PHP_ADD_MAKEFILE_FRAGMENT
+  #PHP_ADD_MAKEFILE_FRAGMENT
 
   PHP_INSTALL_HEADERS([ext/phar], [php_phar.h])
 
--- a/configure.ac
+++ b/configure.ac
@@ -1697,13 +1697,13 @@ CFLAGS_CLEAN="$CFLAGS \$(PROF_FLAGS)"
 CFLAGS="\$(CFLAGS_CLEAN) $standard_libtool_flag"
 CXXFLAGS="$CXXFLAGS $standard_libtool_flag \$(PROF_FLAGS)"
 
-if test "$PHP_PHAR" != "no" && test "$PHP_CLI" != "no"; then
-  pharcmd=pharcmd
-  pharcmd_install=install-pharcmd
-else
+#if test "$PHP_PHAR" != "no" && test "$PHP_CLI" != "no"; then
+#  pharcmd=pharcmd
+#  pharcmd_install=install-pharcmd
+#else
   pharcmd=
   pharcmd_install=
-fi;
+#fi;
 
 all_targets="$lcov_target \$(OVERALL_TARGET) \$(PHP_MODULES) \$(PHP_ZEND_EX) \$(PHP_BINARIES) $pharcmd"
 install_targets="$install_sapi $install_modules $install_binaries install-build install-headers install-programs $install_pear $pharcmd_install"
