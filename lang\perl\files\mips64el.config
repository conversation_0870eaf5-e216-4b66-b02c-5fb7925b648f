owrt:arch=mips64el
owrt:bits=64
owrt:endian=little

ccsymbols=''
cppccsymbols=''
cppsymbols=''
d_casti32='define'
d_double_style_ieee='define'
d_long_double_style_ieee_doubledouble='define'
d_modflproto='undef'
doublekind='3'
fpossize='24'
longdblkind='5'
need_va_copy='define'
quadkind='2'

owrt:sig_count=128
owrt:sigs='ZERO HUP INT QUIT ILL TRAP ABRT EMT FPE KILL BUS SEGV SYS PIPE ALRM TERM USR1 USR2 CHLD PWR WINCH URG IO STOP TSTP CONT TTIN TTOU VTALRM PROF XCPU XFSZ'
owrt:sig_name_extra='IOT CLD POLL'
owrt:sig_num_extra='6 18 22'
