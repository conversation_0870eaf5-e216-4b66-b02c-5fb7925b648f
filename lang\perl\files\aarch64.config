owrt:arch=aarch64
owrt:bits=64
owrt:endian=little

ccsymbols=''
cppccsymbols=''
cppsymbols='__ELF__=1 _FILE_OFFSET_BITS=64 _LARGEFILE_SOURCE=1 _LP64=1 _STDC_PREDEF_H= _XOPEN_SOURCE=700 __AARCH64EL__=1 __AARCH64_CMODEL_SMALL__=1 __ARM_64BIT_STATE=1 __ARM_ARCH=8 __ARM_ARCH_8A=1 __ARM_ARCH_ISA_A64=1 __ARM_ARCH_PROFILE=65 __ARM_FEATURE_CLZ=1 __ARM_FEATURE_FMA=1 __ARM_FEATURE_IDIV=1 __ARM_FEATURE_NUMERIC_MAXMIN=1 __ARM_FEATURE_UNALIGNED=1 __ARM_FP=12 __ARM_NEON=1 __ARM_NEON_FP=12 __ARM_PCS_AAPCS64=1 __ARM_SIZEOF_MINIMAL_ENUM=4 __ARM_SIZEOF_WCHAR_T=4 __ATOMIC_ACQUIRE=2 __ATOMIC_ACQ_REL=4 __ATOMIC_CONSUME=1 __ATOMIC_RELAXED=0 __ATOMIC_RELEASE=3 __ATOMIC_SEQ_CST=5 __BIGGEST_ALIGNMENT__=16 __BYTE_ORDER__=1234 __CHAR16_TYPE__=short\ unsigned\ int __CHAR32_TYPE__=unsigned\ int __CHAR_BIT__=8 __CHAR_UNSIGNED__=1 __DBL_DECIMAL_DIG__=17 __DBL_DENORM_MIN__=((double)4.94065645841246544176568792868221372e-324L) __DBL_DIG__=15 __DBL_EPSILON__=((double)2.22044604925031308084726333618164062e-16L) __DBL_HAS_DENORM__=1 __DBL_HAS_INFINITY__=1 __DBL_HAS_QUIET_NAN__=1 __DBL_MANT_DIG__=53 __DBL_MAX_10_EXP__=308 __DBL_MAX_EXP__=1024 __DBL_MAX__=((double)1.79769313486231570814527423731704357e+308L) __DBL_MIN_10_EXP__=(-307) __DBL_MIN_EXP__=(-1021) __DBL_MIN__=((double)2.22507385850720138309023271733240406e-308L) __DEC128_EPSILON__=1E-33DL __DEC128_MANT_DIG__=34 __DEC128_MAX_EXP__=6145 __DEC128_MAX__=9.999999999999999999999999999999999E6144DL __DEC128_MIN_EXP__=(-6142) __DEC128_MIN__=1E-6143DL __DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL __DEC32_EPSILON__=1E-6DF __DEC32_MANT_DIG__=7 __DEC32_MAX_EXP__=97 __DEC32_MAX__=9.999999E96DF __DEC32_MIN_EXP__=(-94) __DEC32_MIN__=1E-95DF __DEC32_SUBNORMAL_MIN__=0.000001E-95DF __DEC64_EPSILON__=1E-15DD __DEC64_MANT_DIG__=16 __DEC64_MAX_EXP__=385 __DEC64_MAX__=9.999999999999999E384DD __DEC64_MIN_EXP__=(-382) __DEC64_MIN__=1E-383DD __DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD __DECIMAL_DIG__=36 __DEC_EVAL_METHOD__=2 __ELF__=1 __FINITE_MATH_ONLY__=0 __FLOAT_WORD_ORDER__=1234 __FLT_DECIMAL_DIG__=9 __FLT_DENORM_MIN__=1.40129846432481707092372958328991613e-45F __FLT_DIG__=6 __FLT_EPSILON__=1.19209289550781250000000000000000000e-7F __FLT_EVAL_METHOD__=0 __FLT_HAS_DENORM__=1 __FLT_HAS_INFINITY__=1 __FLT_HAS_QUIET_NAN__=1 __FLT_MANT_DIG__=24 __FLT_MAX_10_EXP__=38 __FLT_MAX_EXP__=128 __FLT_MAX__=3.40282346638528859811704183484516925e+38F __FLT_MIN_10_EXP__=(-37) __FLT_MIN_EXP__=(-125) __FLT_MIN__=1.17549435082228750796873653722224568e-38F __FLT_RADIX__=2 __FP_FAST_FMA=1 __FP_FAST_FMAF=1 __GCC_ATOMIC_BOOL_LOCK_FREE=2 __GCC_ATOMIC_CHAR16_T_LOCK_FREE=2 __GCC_ATOMIC_CHAR32_T_LOCK_FREE=2 __GCC_ATOMIC_CHAR_LOCK_FREE=2 __GCC_ATOMIC_INT_LOCK_FREE=2 __GCC_ATOMIC_LLONG_LOCK_FREE=2 __GCC_ATOMIC_LONG_LOCK_FREE=2 __GCC_ATOMIC_POINTER_LOCK_FREE=2 __GCC_ATOMIC_SHORT_LOCK_FREE=2 __GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1 __GCC_ATOMIC_WCHAR_T_LOCK_FREE=2 __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1 __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1 __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1 __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8=1 __GCC_IEC_559=2 __GCC_IEC_559_COMPLEX=2 __GNUC_MINOR__=4 __GNUC_PATCHLEVEL__=0 __GNUC_STDC_INLINE__=1 __GNUC__=5 __INT16_C=__INT16_C __INT16_MAX__=0x7fff __INT16_TYPE__=short\ int __INT32_C=__INT32_C __INT32_MAX__=0x7fffffff __INT32_TYPE__=int __INT64_C=__INT64_C __INT64_MAX__=0x7fffffffffffffffL __INT64_TYPE__=long\ int __INT8_C=__INT8_C __INT8_MAX__=0x7f __INT8_TYPE__=signed\ char __INTMAX_C=__INTMAX_C __INTMAX_MAX__=0x7fffffffffffffffL __INTMAX_TYPE__=long\ int __INTPTR_MAX__=0x7fffffffffffffffL __INTPTR_TYPE__=long\ int __INT_FAST16_MAX__=0x7fffffff __INT_FAST16_TYPE__=int __INT_FAST32_MAX__=0x7fffffff __INT_FAST32_TYPE__=int __INT_FAST64_MAX__=0x7fffffffffffffffL __INT_FAST64_TYPE__=long\ int __INT_FAST8_MAX__=0x7f __INT_FAST8_TYPE__=signed\ char __INT_LEAST16_MAX__=0x7fff __INT_LEAST16_TYPE__=short\ int __INT_LEAST32_MAX__=0x7fffffff __INT_LEAST32_TYPE__=int __INT_LEAST64_MAX__=0x7fffffffffffffffL __INT_LEAST64_TYPE__=long\ int __INT_LEAST8_MAX__=0x7f __INT_LEAST8_TYPE__=signed\ char __INT_MAX__=0x7fffffff __LDBL_DENORM_MIN__=6.47517511943802511092443895822764655e-4966L __LDBL_DIG__=33 __LDBL_EPSILON__=1.92592994438723585305597794258492732e-34L __LDBL_HAS_DENORM__=1 __LDBL_HAS_INFINITY__=1 __LDBL_HAS_QUIET_NAN__=1 __LDBL_MANT_DIG__=113 __LDBL_MAX_10_EXP__=4932 __LDBL_MAX_EXP__=16384 __LDBL_MAX__=1.18973149535723176508575932662800702e+4932L __LDBL_MIN_10_EXP__=(-4931) __LDBL_MIN_EXP__=(-16381) __LDBL_MIN__=3.36210314311209350626267781732175260e-4932L __LONG_LONG_MAX__=0x7fffffffffffffffLL __LONG_MAX__=0x7fffffffffffffffL __LP64__=1 __ORDER_BIG_ENDIAN__=4321 __ORDER_LITTLE_ENDIAN__=1234 __ORDER_PDP_ENDIAN__=3412 __PRAGMA_REDEFINE_EXTNAME=1 __PTRDIFF_MAX__=0x7fffffffffffffffL __PTRDIFF_TYPE__=long\ int __REGISTER_PREFIX__= __SCHAR_MAX__=0x7f __SHRT_MAX__=0x7fff __SIG_ATOMIC_MAX__=0x7fffffff __SIG_ATOMIC_MIN__=(-0x7fffffff\ -\ 1) __SIG_ATOMIC_TYPE__=int __SIZEOF_DOUBLE__=8 __SIZEOF_FLOAT__=4 __SIZEOF_INT128__=16 __SIZEOF_INT__=4 __SIZEOF_LONG_DOUBLE__=16 __SIZEOF_LONG_LONG__=8 __SIZEOF_LONG__=8 __SIZEOF_POINTER__=8 __SIZEOF_PTRDIFF_T__=8 __SIZEOF_SHORT__=2 __SIZEOF_SIZE_T__=8 __SIZEOF_WCHAR_T__=4 __SIZEOF_WINT_T__=4 __SIZE_MAX__=0xffffffffffffffffUL __SIZE_TYPE__=long\ unsigned\ int __STDC_HOSTED__=1 __STDC_IEC_559__=1 __STDC_ISO_10646__=201206L __STDC_UTF_16__=1 __STDC_UTF_32__=1 __STDC__=1 __UINT16_C=__UINT16_C __UINT16_MAX__=0xffff __UINT16_TYPE__=short\ unsigned\ int __UINT32_C=__UINT32_C __UINT32_MAX__=0xffffffffU __UINT32_TYPE__=unsigned\ int __UINT64_C=__UINT64_C __UINT64_MAX__=0xffffffffffffffffUL __UINT64_TYPE__=long\ unsigned\ int __UINT8_C=__UINT8_C __UINT8_MAX__=0xff __UINT8_TYPE__=unsigned\ char __UINTMAX_C=__UINTMAX_C __UINTMAX_MAX__=0xffffffffffffffffUL __UINTMAX_TYPE__=long\ unsigned\ int __UINTPTR_MAX__=0xffffffffffffffffUL __UINTPTR_TYPE__=long\ unsigned\ int __UINT_FAST16_MAX__=0xffffffffU __UINT_FAST16_TYPE__=unsigned\ int __UINT_FAST32_MAX__=0xffffffffU __UINT_FAST32_TYPE__=unsigned\ int __UINT_FAST64_MAX__=0xffffffffffffffffUL __UINT_FAST64_TYPE__=long\ unsigned\ int __UINT_FAST8_MAX__=0xff __UINT_FAST8_TYPE__=unsigned\ char __UINT_LEAST16_MAX__=0xffff __UINT_LEAST16_TYPE__=short\ unsigned\ int __UINT_LEAST32_MAX__=0xffffffffU __UINT_LEAST32_TYPE__=unsigned\ int __UINT_LEAST64_MAX__=0xffffffffffffffffUL __UINT_LEAST64_TYPE__=long\ unsigned\ int __UINT_LEAST8_MAX__=0xff __UINT_LEAST8_TYPE__=unsigned\ char __USER_LABEL_PREFIX__= __WCHAR_MAX__=0xffffffffU __WCHAR_MIN__=0U __WCHAR_TYPE__=unsigned\ int __WINT_MAX__=0xffffffffU __WINT_MIN__=0U __WINT_TYPE__=unsigned\ int __aarch64__=1 __linux=1 __linux__=1 __unix=1 __unix__=1 linux=1 unix=1'
d_casti32='define'
d_double_style_ieee='define'
d_modflproto='define'
doublekind='3'
fpossize='16'
longdblkind='1'
need_va_copy='undef'
quadkind='2'

owrt:sig_count='64'
owrt:sigs='ZERO HUP INT QUIT ILL TRAP ABRT BUS FPE KILL USR1 SEGV USR2 PIPE ALRM TERM STKFLT CHLD CONT STOP TSTP TTIN TTOU URG XCPU XFSZ VTALRM PROF WINCH IO PWR SYS'
owrt:sig_name_extra='IOT CLD POLL UNUSED'
owrt:sig_num_extra='6 17 29 31'
