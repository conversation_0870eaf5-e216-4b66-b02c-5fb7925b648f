--- config.pl	2018-03-11 09:30:28.000000000 +0100
+++ config.pl.new	2018-04-05 08:40:29.180000000 +0200
@@ -41,7 +41,10 @@
 #
 # Host name on which the BackupPC server is running.
 #
-$Conf{ServerHost} = 'XXXXXX';
+$ENV{'PATH'} = '/bin:/usr/bin';
+delete @ENV{'IFS', 'CDPATH', 'ENV', 'BASH_ENV'};
+$Conf{ServerHost} = $ENV{'HOSTNAME'};
+chomp($Conf{ServerHost});
 
 #
 # TCP port number on which the BackupPC server listens for and accepts
@@ -223,7 +226,7 @@
 #
 # Full path to various commands for archiving
 #
-$Conf{SplitPath} = '/usr/bin/split';
+$Conf{SplitPath} = '/usr/bin/split' if ( -x '/usr/bin/split' );
 $Conf{ParPath}   = '';
 $Conf{CatPath}   = '/bin/cat';
 $Conf{GzipPath}  = '/bin/gzip';
@@ -1572,7 +1575,7 @@
 # Full path for ssh. Security caution: normal users should not
 # allowed to write to this file or directory.
 #
-$Conf{SshPath} = '/usr/bin/ssh';
+$Conf{SshPath} = '/usr/bin/ssh' if ( -x '/usr/bin/ssh' );
 
 #
 # Full path for nmblookup. Security caution: normal users should not
@@ -1874,7 +1877,7 @@
 # Full path to the sendmail command.  Security caution: normal users
 # should not allowed to write to this file or directory.
 #
-$Conf{SendmailPath} = '/usr/sbin/sendmail';
+$Conf{SendmailPath} = '/usr/sbin/sendmail' if ( -x '/usr/sbin/sendmail' );
 
 #
 # Minimum period between consecutive emails to a single user.
@@ -2028,13 +2031,13 @@
 #    $Conf{CgiAdminUsers}     = 'craig celia';
 #    --> administrative users are only craig and celia'.
 #
-$Conf{CgiAdminUserGroup} = '';
-$Conf{CgiAdminUsers}     = '';
+$Conf{CgiAdminUserGroup} = 'backuppc';
+$Conf{CgiAdminUsers}     = 'backuppc';
 
 #
 # URL of the BackupPC_Admin CGI script.  Used for email messages.
 #
-$Conf{CgiURL} = 'http://XXXXXX/cgi-bin//BackupPC_Admin';
+$Conf{CgiURL} = 'http://' . $Conf{ServerHost} . '/cgi-bin//BackupPC_Admin';
 
 #   
 # Language to use.  See lib/BackupPC/Lang for the list of supported
@@ -2076,7 +2079,7 @@
 # dates (MM/DD), a value of 2 uses full YYYY-MM-DD format, and zero
 # for international dates (DD/MM).
 #
-$Conf{CgiDateFormatMMDD} = 1;
+$Conf{CgiDateFormatMMDD} = 2;
 
 #
 # If set, the complete list of hosts appears in the left navigation
