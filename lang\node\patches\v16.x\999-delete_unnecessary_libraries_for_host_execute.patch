--- a/tools/icu/icu-generic.gyp
+++ b/tools/icu/icu-generic.gyp
@@ -419,6 +419,7 @@
       'target_name': 'genrb',
       'type': 'executable',
       'toolsets': [ 'host' ],
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_genrb)'
@@ -435,6 +436,7 @@
       'target_name': 'iculslocs',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         'iculslocs.cc',
@@ -447,6 +449,7 @@
       'target_name': 'icupkg',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_icupkg)',
@@ -458,6 +461,7 @@
       'target_name': 'genccode',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_genccode)',
--- a/tools/v8_gypfiles/v8.gyp
+++ b/tools/v8_gypfiles/v8.gyp
@@ -1373,6 +1373,7 @@
     {
       'target_name': 'bytecode_builtins_list_generator',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'conditions': [
         ['want_separate_host_toolset', {
           'toolsets': ['host'],
@@ -1397,6 +1398,8 @@
     {
       'target_name': 'mksnapshot',
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'library_dirs':[ '../../../../staging_dir/hostpkg/share/icu/current/lib' ],
       'dependencies': [
         'v8_base_without_compiler',
         'v8_compiler_for_mksnapshot',
@@ -1418,6 +1421,7 @@
     {
       'target_name': 'torque',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [
         'torque_base',
         # "build/win:default_exe_manifest",
@@ -1456,6 +1460,7 @@
     {
       'target_name': 'torque-language-server',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'conditions': [
         ['want_separate_host_toolset', {
           'toolsets': ['host'],
@@ -1483,6 +1488,8 @@
     {
       'target_name': 'gen-regexp-special-case',
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'library_dirs':[ '../../../../staging_dir/hostpkg/share/icu/current/lib' ],
       'dependencies': [
         'v8_libbase',
         # "build/win:default_exe_manifest",
