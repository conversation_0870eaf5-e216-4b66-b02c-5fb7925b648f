--- a/lib/internal/modules/cjs/loader.js
+++ b/lib/internal/modules/cjs/loader.js
@@ -1109,7 +1109,8 @@ Module._initPaths = function() {
     path.resolve(process.execPath, '..') :
     path.resolve(process.execPath, '..', '..');
 
-  let paths = [path.resolve(prefixDir, 'lib', 'node')];
+  let paths = [path.resolve(prefixDir, 'lib', 'node'),
+               path.resolve(prefixDir, 'lib', 'node_modules')];
 
   if (homeDir) {
     paths.unshift(path.resolve(homeDir, '.node_libraries'));
