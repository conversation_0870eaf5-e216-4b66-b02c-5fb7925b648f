--- a/tools/icu/icu-generic.gyp
+++ b/tools/icu/icu-generic.gyp
@@ -106,6 +106,7 @@
           'sources': [
             '<@(icu_src_i18n)'
           ],
+          'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
           'include_dirs': [
             '<(icu_path)/source/i18n',
           ],
@@ -114,6 +115,7 @@
           ],
           'dependencies': [ 'icuucx', 'icu_implementation', 'icu_uconfig', 'icu_uconfig_target' ],
           'direct_dependent_settings': {
+            'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
             'include_dirs': [
               '<(icu_path)/source/i18n',
             ],
@@ -223,6 +225,7 @@
               # full data - no trim needed
               'sources': [ '<(SHARED_INTERMEDIATE_DIR)/icudt<(icu_ver_major)_dat.<(icu_asm_ext)' ],
               'dependencies': [ 'genccode#host', 'icupkg#host', 'icu_implementation#host', 'icu_uconfig' ],
+              'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
               'include_dirs': [
                 '<(icu_path)/source/common',
               ],
@@ -307,6 +310,7 @@
               # This file contains the small ICU data
               'sources': [ '<(SHARED_INTERMEDIATE_DIR)/icusmdt<(icu_ver_major)_dat.<(icu_asm_ext)' ],
               # for umachine.h
+              'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
               'include_dirs': [
                 '<(icu_path)/source/common',
               ],
@@ -323,6 +327,7 @@
       'sources': [
         '<@(icu_src_stubdata)'
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
       ],
@@ -362,6 +367,7 @@
           '_XOPEN_SOURCE_EXTENDED=0',
         ]}],
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
       ],
@@ -371,6 +377,7 @@
       'cflags_c': ['-std=c99'],
       'export_dependent_settings': [ 'icu_uconfig', 'icu_uconfig_target' ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(icu_path)/source/common',
         ],
@@ -401,6 +408,7 @@
         '<(icu_path)/source/tools/toolutil/dbgutil.cpp',
         '<(icu_path)/source/tools/toolutil/dbgutil.h',
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
         '<(icu_path)/source/i18n',
@@ -420,6 +428,7 @@
         }]
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(icu_path)/source/common',
           '<(icu_path)/source/i18n',
@@ -441,6 +450,7 @@
       'target_name': 'genrb',
       'type': 'executable',
       'toolsets': [ 'host' ],
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools', 'icu_implementation' ],
       'sources': [
         '<@(icu_src_genrb)'
@@ -463,6 +473,7 @@
       'target_name': 'iculslocs',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         'iculslocs.cc',
@@ -481,6 +492,7 @@
       'target_name': 'icupkg',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_icupkg)',
@@ -498,6 +510,7 @@
       'target_name': 'genccode',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_genccode)',
