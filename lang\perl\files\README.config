-- Perl configuration --

Per<PERSON> uses a huge configuration file, normally generated via the Configure script
at build-time. This fails when cross-compiling though, so we need to supply our
own.

We're using perlconfig.pl to piece together the final configuration from a bunch
of configuration files(all ending in .config). Please refer to perlconfig.pl's
POD for information on usage and syntax.

Throughout the files, you will see a bunch of references to private symbols with
the prefix "owrt". These are used to control output in an effort to both
simplify writing configuration files, as well as to provide switchable options
to select the feature set of the resulting perl installation.

The following will be a summary/quick reference of all private symbols we're
currently using:

Passed via architecture configuration file(mipsel.config, i486.config, ...)
---------------------------------------------------------------------------
Symbol              Values              Description
owrt:bits           32/64               Target's native word length.
owrt:endian         little/big          Target's endianness.
owrt:arch           mipsel, i486, ...   Target's architecture name.
owrt:sig_count      64/128              Number of signals the target
                                        provides(NSIG - 1).
owrt:sigs           *                   Symbolic names of the first 32 signals
                                        this architecture provides, in numeric
                                        order. Separated by whitespaces.
owrt:sig_name_extra *                   Symbolic names of any additional signals
                                        this architecture provides after
                                        owrt:sig_count. Separated by
                                        whitespaces.
owrt:sig_num_extra  *                   Numeric values associated with the
                                        signal names provided in
                                        owrt:sig_name_extra. Separated by
                                        whitespaces.

Passed via command line
-----------------------
Symbol                Values              Description
owrt:libc             glibc/uclibc/musl   Which C library implementation is in
                                          use.
owrt:threads          yes/no              Whether to enable threading support.
owrt:ipv6             define/undef        Whether to enable IPv6 support.
owrt:target_cross     *                   Target architecture's host triplet.
owrt:target_cc        *                   C compiler to use.
owrt:gccversion       *                   target_cc's version number.
owrt:cflags           *                   Additional C compiler flags.
owrt:ldflags          *                   Additional linker flags.
owrt:staging_dir      *                   Same as OpenWRT buildroot's
                                          $(STAGING_DIR).
owrt:host_perl_prefix *                   host-perl installation prefix.

Passed via version.config
-------------------------
Symbol           Values              Description
owrt:perllibpath *                   Path to perl library files, from the
                                     target's point of view.
