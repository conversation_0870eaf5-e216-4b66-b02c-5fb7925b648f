--- a/lpc21isp.c
+++ b/lpc21isp.c
@@ -1549,7 +1549,7 @@ static void ReadArguments(ISP_ENVIRONMEN
         DebugPrintf(2, "\n"
                        "Portable command line ISP\n"
                        "for NXP LPC family and Analog Devices ADUC 70xx\n"
-                       "Version " VERSION_STR " compiled for " COMPILED_FOR ": " __DATE__ ", " __TIME__ "\n"
+                       "Version " VERSION_STR " compiled for " COMPILED_FOR "\n"
                        "Copyright (c) by <PERSON>, 2003-2013, Email: <EMAIL>\n"
                        "Portions Copyright (c) by Aeolus Development 2004, www.aeolusdevelopment.com\n"
                        "\n");
