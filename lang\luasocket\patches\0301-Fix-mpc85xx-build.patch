--- a/src/makefile
+++ b/src/makefile
@@ -397,18 +397,18 @@ none:
 all: $(SOCKET_SO) $(MIME_SO)
 
 $(SOCKET_SO): $(SOCKET_OBJS)
-	$(LD) $(SOCKET_OBJS) $(LDFLAGS)$@
+	$(CC) $(SOCKET_OBJS) $(LDFLAGS)$@
 
 $(MIME_SO): $(MIME_OBJS)
-	$(LD) $(MIME_OBJS) $(LDFLAGS)$@
+	$(CC) $(MIME_OBJS) $(LDFLAGS)$@
 
 all-unix: all $(UNIX_SO) $(SERIAL_SO)
 
 $(UNIX_SO): $(UNIX_OBJS)
-	$(LD) $(UNIX_OBJS) $(LDFLAGS)$@
+	$(CC) $(UNIX_OBJS) $(LDFLAGS)$@
 
 $(SERIAL_SO): $(SERIAL_OBJS)
-	$(LD) $(SERIAL_OBJS) $(LDFLAGS)$@
+	$(CC) $(SERIAL_OBJS) $(LDFLAGS)$@
 
 install:
 	$(INSTALL_DIR) $(INSTALL_TOP_LDIR)
