From cf39da53236abf02d39c6a98a645488933f3e861 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 21 Aug 2018 19:29:07 -0700
Subject: [PATCH] ipmitool: Fix compile with deprecated APIs disabled.

From the man page:

EVP_CIPHER_CTX was made opaque in OpenSSL 1.1.0. As a result,
EVP_CIPHER_CTX_reset() appeared and EVP_CIPHER_CTX_cleanup() disappeared.
EVP_CIPHER_CTX_init() remains as an alias for EVP_CIPHER_CTX_reset().

Signed-off-by: <PERSON> <<EMAIL>>
---
 src/plugins/lanplus/lanplus_crypt_impl.c | 8 ++++++++
 1 file changed, 8 insertions(+)

--- a/src/plugins/lanplus/lanplus_crypt_impl.c
+++ b/src/plugins/lanplus/lanplus_crypt_impl.c
@@ -183,7 +183,11 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 		lprintf(LOG_DEBUG, "ERROR: EVP_CIPHER_CTX_new() failed");
 		return;
 	}
+#if OPENSSL_VERSION_NUMBER < 0x10100000L
 	EVP_CIPHER_CTX_init(ctx);
+#else
+	EVP_CIPHER_CTX_reset(ctx);
+#endif
 	EVP_EncryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
 	EVP_CIPHER_CTX_set_padding(ctx, 0);
 
@@ -262,7 +266,11 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 		lprintf(LOG_DEBUG, "ERROR: EVP_CIPHER_CTX_new() failed");
 		return;
 	}
+#if OPENSSL_VERSION_NUMBER < 0x10100000L
 	EVP_CIPHER_CTX_init(ctx);
+#else
+	EVP_CIPHER_CTX_reset(ctx);
+#endif
 	EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
 	EVP_CIPHER_CTX_set_padding(ctx, 0);
 
