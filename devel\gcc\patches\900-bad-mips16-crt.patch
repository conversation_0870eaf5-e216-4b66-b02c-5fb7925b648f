commit 9dc38e48f7a6f88b7ac7bfaced91f53660204e46
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Fri Apr 5 12:36:06 2013 +0000

    toolchain/gcc: .init and .fini need to pick one ISA
    
    The .init and .fini sections are built by concatenating code
    fragments. Putting mips16 code in the middle of a mips32 code block
    doesn't work. Make gcc built the magic crt stuff in no-mips16 mode.
    
    This is specific to 4.6-linaro but is probably portable to other gcc
    flavors. Adding this to the t-libgcc-mips16 makefile fragment is a
    hack not suitable for pushing upstream, but there is no mips/t-linux
    or mips/t-uclibc and I am not going to touch gcc/configure for two
    lines.
    
    Signed-off-by: <PERSON> <<EMAIL>>
    Signed-off-by: <PERSON><PERSON><PERSON> <<EMAIL>>
    
    SVN-Revision: 36200

--- a/libgcc/config/mips/t-mips16
+++ b/libgcc/config/mips/t-mips16
@@ -43,3 +43,6 @@ SYNC_CFLAGS = -mno-mips16
 
 # Version these symbols if building libgcc.so.
 SHLIB_MAPFILES += $(srcdir)/config/mips/libgcc-mips16.ver
+
+CRTSTUFF_T_CFLAGS += -mno-mips16
+CRTSTUFF_T_CFLAGS_S += -mno-mips16
