# SPDX-Identifier-License: GPL-2.0-only
#
# Copyright (C) 2022 W<PERSON> <PERSON> <<EMAIL>>

include $(TOPDIR)/rules.mk

PKG_NAME:=flex
PKG_VERSION:=2.6.4
PKG_RELEASE:=$(AUTORELEASE)
PKG_CPE_ID:=cpe:/a:flex_project:flex

PKG_SOURCE:=$(PKG_NAME)-$(PKG_VERSION).tar.gz
PKG_SOURCE_URL:=https://github.com/westes/flex/releases/download/v$(PKG_VERSION)/
PKG_HASH:=e87aae032bf07c26f85ac0ed3250998c37621d95f8bd748b31f15b33c45ee995

PKG_FIXUP:=autoreconf gettext-version

PKG_MAINTAINER:=W. <PERSON> <<EMAIL>>
PKG_LICENSE:=BSD-2-Clause
PKG_LICENSE_FILES:=COPYING

PKG_INSTALL:=1

include $(INCLUDE_DIR)/package.mk

define Package/flex
  SECTION:=devel
  CATEGORY:=Development
  TITLE:=flex
  URL:=https://github.com/westes/flex
endef

define Package/flex/description
  flex is a tool for generating scanners: programs which recognize lexical patterns in text
endef

CONFIGURE_ARGS += --disable-shared --disable-bootstrap

define Build/InstallDev
	$(INSTALL_DIR) $(1)/usr/include $(1)/usr/lib
	$(CP) $(PKG_INSTALL_DIR)/usr/include/* $(1)/usr/include/
	$(CP) $(PKG_INSTALL_DIR)/usr/lib/* $(1)/usr/lib/
endef

define Package/flex/install
	$(INSTALL_DIR) $(1)/usr/bin/
	$(CP) \
		$(PKG_INSTALL_DIR)/usr/bin/flex \
		$(1)/usr/bin/
endef

$(eval $(call BuildPackage,flex))
