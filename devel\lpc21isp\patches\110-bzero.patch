--- a/lpc21isp.c
+++ b/lpc21isp.c
@@ -533,7 +533,7 @@ static void OpenSerialPort(ISP_ENVIRONME
 
     tcgetattr(IspEnvironment->fdCom, &IspEnvironment->oldtio); /* save current port settings */
 
-    bzero(&IspEnvironment->newtio, sizeof(IspEnvironment->newtio));
+    memset(&IspEnvironment->newtio, 0, sizeof(IspEnvironment->newtio));
     IspEnvironment->newtio.c_cflag = CS8 | CLOCAL | CREAD;
 
 #if defined(__FreeBSD__) || defined(__OpenBSD__)
