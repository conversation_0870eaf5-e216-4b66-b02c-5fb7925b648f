--- a/deps/v8/src/trap-handler/trap-handler.h
+++ b/deps/v8/src/trap-handler/trap-handler.h
@@ -17,19 +17,16 @@ namespace v8 {
 namespace internal {
 namespace trap_handler {
 
-// X64 on Linux, Windows, MacOS, FreeBSD.
-#if V8_HOST_ARCH_X64 && V8_TARGET_ARCH_X64 &&                        \
-    ((V8_OS_LINUX && !V8_OS_ANDROID) || V8_OS_WIN || V8_OS_MACOSX || \
-     V8_OS_FREEBSD)
+#if V8_TARGET_ARCH_X64 && V8_OS_LINUX && !V8_OS_ANDROID
 #define V8_TRAP_HANDLER_SUPPORTED true
-// Arm64 (non-simulator) on Mac.
-#elif V8_TARGET_ARCH_ARM64 && V8_HOST_ARCH_ARM64 && V8_OS_MACOSX
+#elif V8_TARGET_ARCH_X64 && V8_OS_WIN
 #define V8_TRAP_HANDLER_SUPPORTED true
-// Arm64 simulator on x64 on Linux or Mac.
-#elif V8_TARGET_ARCH_ARM64 && V8_HOST_ARCH_X64 && (V8_OS_LINUX || V8_OS_MACOSX)
-#define V8_TRAP_HANDLER_VIA_SIMULATOR
+#elif V8_TARGET_ARCH_X64 && V8_OS_MACOSX
+#define V8_TRAP_HANDLER_SUPPORTED true
+#elif V8_TARGET_ARCH_X64 && V8_OS_FREEBSD
+#define V8_TRAP_HANDLER_SUPPORTED true
+#elif V8_HOST_ARCH_ARM64 && V8_TARGET_ARCH_ARM64 && V8_OS_MACOSX
 #define V8_TRAP_HANDLER_SUPPORTED true
-// Everything else is unsupported.
 #else
 #define V8_TRAP_HANDLER_SUPPORTED false
 #endif
