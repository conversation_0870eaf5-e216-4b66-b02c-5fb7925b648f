--- a/config_h.SH
+++ b/config_h.SH
@@ -52,6 +52,19 @@ sed <<!GROK!THIS! >$CONFIG_H -e 's!^#und
 #ifndef _config_h_
 #define _config_h_
 
+#if defined(USE_CROSS_COMPILE) && !defined(__LITTLE_ENDIAN__) && !defined(__BIG_ENDIAN__)
+#  include <endian.h>
+#  if defined(__BYTE_ORDER) && defined(__LITTLE_ENDIAN) && defined(__BIG_ENDIAN)
+#    if (__BYTE_ORDER == __LITTLE_ENDIAN)
+#      define __LITTLE_ENDIAN__
+#    elif (__BYTE_ORDER == __BIG_ENDIAN)
+#      define __BIG_ENDIAN__
+#    else
+#      error Unknown endianness
+#    endif
+#  endif
+#endif
+
 /* LOC_SED:
  *	This symbol holds the complete pathname to the sed program.
  */
