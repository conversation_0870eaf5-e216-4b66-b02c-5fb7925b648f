--- a/lib/sigsegv.c
+++ b/lib/sigsegv.c
@@ -219,8 +219,8 @@ int libsigsegv_version = LIBSIGSEGV_VERS
 #   define SIGSEGV_FAULT_STACKPOINTER  ((ucontext_t *) ucp)->uc_mcontext.gp_regs[1]
 #  else /* 32-bit */
 /* both should be equivalent */
-#   if 0
-#    define SIGSEGV_FAULT_STACKPOINTER  ((ucontext_t *) ucp)->uc_mcontext.regs->gpr[1]
+#   ifndef __GLIBC__
+#    define SIGSEGV_FAULT_STACKPOINTER  ((ucontext_t *) ucp)->uc_mcontext.gregs[1]
 #   else
 #    define SIGSEGV_FAULT_STACKPOINTER  ((ucontext_t *) ucp)->uc_mcontext.uc_regs->gregs[1]
 #   endif
