From 49627124928f066320b7c2d2ba7501eac5c4fc63 Mon Sep 17 00:00:00 2001
From: qiangx<PERSON><PERSON> <<EMAIL>>
Date: Mon, 1 Apr 2024 07:16:47 +0000
Subject: [PATCH] loongarch64 support for fibers

Add loongarch64 assembly files from Boost, needed for fibers support,
and hook up loongarch64 fibers support during configure.

Close GH-13914
---
 Zend/asm/jump_loongarch64_sysv_elf_gas.S | 121 +++++++++++++++++++++++
 Zend/asm/make_loongarch64_sysv_elf_gas.S |  72 ++++++++++++++
 configure.ac                             |   2 +
 3 files changed, 195 insertions(+)
 create mode 100644 Zend/asm/jump_loongarch64_sysv_elf_gas.S
 create mode 100644 Zend/asm/make_loongarch64_sysv_elf_gas.S

--- /dev/null
+++ b/Zend/asm/jump_loongarch64_sysv_elf_gas.S
@@ -0,0 +1,121 @@
+/*******************************************************
+ *                                                     *
+ *  -------------------------------------------------  *
+ *  |  0  |  1  |  2  |  3  |  4  |  5  |  6  |  7  |  *
+ *  -------------------------------------------------  *
+ *  |     0     |     8     |    16     |     24    |  *
+ *  -------------------------------------------------  *
+ *  |    FS0    |    FS1    |    FS2    |    FS3    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  8  |  9  |  10 |  11 |  12 |  13 |  14 |  15 |  *
+ *  -------------------------------------------------  *
+ *  |     32    |    40     |     48    |     56    |  *
+ *  -------------------------------------------------  *
+ *  |    FS4    |    FS5    |    FS6    |    FS7    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  16 |  17 |  18 |  19 |  20 |  21 |  22 |  23 |  *
+ *  -------------------------------------------------  *
+ *  |     64    |    72     |     80    |     88    |  *
+ *  -------------------------------------------------  *
+ *  |    S0     |    S1     |     S2    |     S3    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  24 |  25 |  26 |  27 |  28 |  29 |  30 |  31 |  *
+ *  -------------------------------------------------  *
+ *  |  96 | 100 | 104 | 108 | 112 | 116 | 120 | 124 |  *
+ *  -------------------------------------------------  *
+ *  |    S4     |    S5     |     S6    |     S7    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  32 |  33 |  34 |  35 |  36 |  37 |  38 |  39 |  *
+ *  -------------------------------------------------  *
+ *  | 128 | 132 | 136 | 140 | 144 | 148 | 152 | 156 |  *
+ *  -------------------------------------------------  *
+ *  |    S8     |    FP     |     RA    |     PC    |  *
+ *  -------------------------------------------------  *
+ *                                                     *
+ * *****************************************************/
+
+.file "jump_loongarch64_sysv_elf_gas.S"
+.text
+.globl jump_fcontext
+.align 2
+.type jump_fcontext,@function
+jump_fcontext:
+    # reserve space on stack
+    addi.d  $sp, $sp, -160
+
+    # save fs0 - fs7
+    fst.d  $fs0, $sp, 0
+    fst.d  $fs1, $sp, 8
+    fst.d  $fs2, $sp, 16
+    fst.d  $fs3, $sp, 24
+    fst.d  $fs4, $sp, 32
+    fst.d  $fs5, $sp, 40
+    fst.d  $fs6, $sp, 48
+    fst.d  $fs7, $sp, 56
+
+    # save s0 - s8, fp, ra
+    st.d  $s0, $sp, 64
+    st.d  $s1, $sp, 72
+    st.d  $s2, $sp, 80
+    st.d  $s3, $sp, 88
+    st.d  $s4, $sp, 96
+    st.d  $s5, $sp, 104
+    st.d  $s6, $sp, 112
+    st.d  $s7, $sp, 120
+    st.d  $s8, $sp, 128
+    st.d  $fp, $sp, 136
+    st.d  $ra, $sp, 144
+
+    # save RA as PC
+    st.d  $ra, $sp, 152
+
+    # store SP (pointing to context-data) in A2
+    move  $a2, $sp
+
+    # restore SP (pointing to context-data) from A0
+    move  $sp, $a0
+
+    # load fs0 - fs7
+    fld.d  $fs0, $sp, 0
+    fld.d  $fs1, $sp, 8
+    fld.d  $fs2, $sp, 16
+    fld.d  $fs3, $sp, 24
+    fld.d  $fs4, $sp, 32
+    fld.d  $fs5, $sp, 40
+    fld.d  $fs6, $sp, 48
+    fld.d  $fs7, $sp, 56
+
+    #load s0 - s7
+    ld.d  $s0, $sp, 64
+    ld.d  $s1, $sp, 72
+    ld.d  $s2, $sp, 80
+    ld.d  $s3, $sp, 88
+    ld.d  $s4, $sp, 96
+    ld.d  $s5, $sp, 104
+    ld.d  $s6, $sp, 112
+    ld.d  $s7, $sp, 120
+    ld.d  $s8, $sp, 128
+    ld.d  $fp, $sp, 136
+    ld.d  $ra, $sp, 144
+
+    # return transfer_t from jump
+    # pass transfer_t as first arg in context function
+    # a0 == FCTX, a1 == DATA
+    move  $a0, $a2
+
+    # load PC
+    ld.d  $a2, $sp, 152
+
+    # restore stack
+    addi.d  $sp, $sp, 160
+
+    # jump to context
+    jr  $a2
+.size jump_fcontext, .-jump_fcontext
+
+/* Mark that we don't need executable stack.  */
+.section .note.GNU-stack,"",%progbits
--- /dev/null
+++ b/Zend/asm/make_loongarch64_sysv_elf_gas.S
@@ -0,0 +1,72 @@
+/*******************************************************
+ *                                                     *
+ *  -------------------------------------------------  *
+ *  |  0  |  1  |  2  |  3  |  4  |  5  |  6  |  7  |  *
+ *  -------------------------------------------------  *
+ *  |     0     |     8     |    16     |     24    |  *
+ *  -------------------------------------------------  *
+ *  |    FS0    |    FS1    |    FS2    |    FS3    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  8  |  9  |  10 |  11 |  12 |  13 |  14 |  15 |  *
+ *  -------------------------------------------------  *
+ *  |     32    |    40     |     48    |     56    |  *
+ *  -------------------------------------------------  *
+ *  |    FS4    |    FS5    |    FS6    |    FS7    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  16 |  17 |  18 |  19 |  20 |  21 |  22 |  23 |  *
+ *  -------------------------------------------------  *
+ *  |     64    |    72     |     80    |     88    |  *
+ *  -------------------------------------------------  *
+ *  |    S0     |    S1     |     S2    |     S3    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  24 |  25 |  26 |  27 |  28 |  29 |  30 |  31 |  *
+ *  -------------------------------------------------  *
+ *  |  96 | 100 | 104 | 108 | 112 | 116 | 120 | 124 |  *
+ *  -------------------------------------------------  *
+ *  |    S4     |    S5     |     S6    |     S7    |  *
+ *  -------------------------------------------------  *
+ *  -------------------------------------------------  *
+ *  |  32 |  33 |  34 |  35 |  36 |  37 |  38 |  39 |  *
+ *  -------------------------------------------------  *
+ *  | 128 | 132 | 136 | 140 | 144 | 148 | 152 | 156 |  *
+ *  -------------------------------------------------  *
+ *  |    S8     |    FP     |     RA    |     PC    |  *
+ *  -------------------------------------------------  *
+ *                                                     *
+ * *****************************************************/
+
+.file "make_loongarch64_sysv_elf_gas.S"
+.text
+.globl make_fcontext
+.align 2
+.type make_fcontext,@function
+make_fcontext:
+    # shift address in A0 to lower 16 byte boundary
+    bstrins.d $a0, $zero, 3, 0
+
+    # reserve space for context-data on context-stack
+    addi.d  $a0, $a0, -160
+
+    # third arg of make_fcontext() == address of context-function
+    st.d  $a2, $a0, 152
+
+    # save address of finish as return-address for context-function
+    # will be entered after context-function returns
+    la.local  $a4, finish
+    st.d  $a4, $a0, 144
+
+    # return pointer to context-data
+    jr  $ra
+
+finish:
+    # exit code is zero
+    li.d  $a0, 0
+    # call _exit(0)
+    b  %plt(_exit)
+
+.size make_fcontext, .-make_fcontext
+/* Mark that we don't need executable stack.  */
+.section .note.GNU-stack,"",%progbits
--- a/configure.ac
+++ b/configure.ac
@@ -1281,6 +1281,7 @@ AS_CASE([$host_cpu],
   [ppc*|powerpc*], [fiber_cpu="ppc32"],
   [riscv64*], [fiber_cpu="riscv64"],
   [s390x*], [fiber_cpu="s390x"],
+  [loongarch64*], [fiber_cpu="loongarch64"],
   [mips64*], [fiber_cpu="mips64"],
   [mips*], [fiber_cpu="mips32"],
   [fiber_cpu="unknown"]
@@ -1302,6 +1303,7 @@ AS_CASE([$fiber_cpu],
   [ppc32], [fiber_asm_file_prefix="ppc32_sysv"],
   [riscv64], [fiber_asm_file_prefix="riscv64_sysv"],
   [s390x], [fiber_asm_file_prefix="s390x_sysv"],
+  [loongarch64], [fiber_asm_file_prefix="loongarch64_sysv"],
   [mips64], [fiber_asm_file_prefix="mips64_n64"],
   [mips32], [fiber_asm_file_prefix="mips32_o32"],
   [fiber_asm_file_prefix="unknown"]
