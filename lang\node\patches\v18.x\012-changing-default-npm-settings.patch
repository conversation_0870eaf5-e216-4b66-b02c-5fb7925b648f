--- a/deps/npm/lib/utils/config/definitions.js
+++ b/deps/npm/lib/utils/config/definitions.js
@@ -1266,7 +1266,7 @@ define('lockfile-version', {
 })
 
 define('loglevel', {
-  default: 'notice',
+  default: 'info',
   type: [
     'silent',
     'error',
@@ -2045,7 +2045,7 @@ define('strict-peer-deps', {
 })
 
 define('strict-ssl', {
-  default: true,
+  default: false,
   type: Boolean,
   description: `
     Whether or not to do SSL key validation when making requests to the
