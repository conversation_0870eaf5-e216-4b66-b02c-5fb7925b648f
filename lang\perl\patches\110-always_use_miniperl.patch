--- a/Makefile.SH
+++ b/Makefile.SH
@@ -339,22 +339,11 @@ MANIFEST_SRT = MANIFEST.srt
 
 !GROK!THIS!
 
-case "$usecrosscompile$perl" in
-define?*)
-	$spitshell >>$Makefile <<!GROK!THIS!
-# Macros to invoke a copy of our fully operational perl during the build.
-PERL_EXE = perl\$(EXE_EXT)
-RUN_PERL = \$(LDLIBPTH) \$(RUN) $perl\$(EXE_EXT)
-!GROK!THIS!
-	;;
-*)
-	$spitshell >>$Makefile <<!GROK!THIS!
+$spitshell >>$Makefile <<!GROK!THIS!
 # Macros to invoke a copy of our fully operational perl during the build.
 PERL_EXE = perl\$(EXE_EXT)
-RUN_PERL = \$(LDLIBPTH) \$(RUN) ./perl\$(EXE_EXT) -Ilib -I.
+RUN_PERL = \$(LDLIBPTH) \$(RUN) ./miniperl\$(EXE_EXT) -Ilib -I.
 !GROK!THIS!
-	;;
-esac
 
 $spitshell >>$Makefile <<!GROK!THIS!
 # Macros to run our tests
