--- a/src/libs/zbxcommon/str.c
+++ b/src/libs/zbxcommon/str.c
@@ -49,7 +49,7 @@ static const char	help_message_footer[]
 void	zbx_version(void)
 {
 	printf("%s (Zabbix) %s\n", title_message, ZABBIX_VERSION);
-	printf("Revision %s %s, compilation time: %s %s\n\n", ZABBIX_REVISION, ZABBIX_REVDATE, __DATE__, __TIME__);
+	printf("Revision %s %s\n\n", ZABBIX_REVISION, ZABBIX_REVDATE);
 	puts(copyright_message);
 }
 
