From da45b3fde60095756f5f6030f6012c23a3d34429 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 3 Oct 2014 19:09:00 +0930
Subject: Add .note.GNU-stack section

See http://lists.busybox.net/pipermail/uclibc/2014-October/048671.html
Below copied from https://gcc.gnu.org/ml/gcc-patches/2014-09/msg02430.html

Re: [Patch, MIPS] Add .note.GNU-stack section

    From: <PERSON> <sellcey at mips dot com>

On Wed, 2014-09-10 at 10:15 -0700, <PERSON> wrote:
>
>
> On Wed, Sep 10, 2014 at 9:27 AM, <<EMAIL>> wrote:

>         This works except you did not update the assembly files in
>         libgcc or glibc. We (Cavium) have the same patch in our tree
>         for a few released versions.

> Mind just checking yours in then Andrew?

> Thanks!
> -eric

I talked to <PERSON> about what files he changed in GCC and created and
tested this new patch.  <PERSON> also mentioned changing some assembly
files in glibc but I don't see any use of '.section .note.GNU-stack' in
any assembly files in glibc (for any platform) so I wasn't planning on
creating a glibc to add them to mips glibc assembly language files.

OK to check in this patch?

<PERSON>lcey
<EMAIL>



2014-09-26  Steve <PERSON>lcey  <<EMAIL>>
---
 gcc/config/mips/mips.c          | 3 +++
 libgcc/config/mips/crti.S       | 4 ++++
 libgcc/config/mips/crtn.S       | 3 +++
 libgcc/config/mips/mips16.S     | 4 ++++
 libgcc/config/mips/vr4120-div.S | 4 ++++
 5 files changed, 18 insertions(+)

--- a/gcc/config/mips/mips.c
+++ b/gcc/config/mips/mips.c
@@ -22567,6 +22567,9 @@ mips_promote_function_mode (const_tree t
 #undef TARGET_CUSTOM_FUNCTION_DESCRIPTORS
 #define TARGET_CUSTOM_FUNCTION_DESCRIPTORS 2
 
+#undef TARGET_ASM_FILE_END
+#define TARGET_ASM_FILE_END file_end_indicate_exec_stack
+
 struct gcc_target targetm = TARGET_INITIALIZER;
 
 #include "gt-mips.h"
--- a/libgcc/config/mips/crti.S
+++ b/libgcc/config/mips/crti.S
@@ -21,6 +21,10 @@ a copy of the GCC Runtime Library Except
 see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
 <http://www.gnu.org/licenses/>.  */
 
+
+/* An executable stack is *not* required for these functions.  */
+	.section .note.GNU-stack,"",%progbits
+
 /* 4 slots for argument spill area.  1 for cpreturn, 1 for stack.
    Return spill offset of 40 and 20.  Aligned to 16 bytes for n32.  */
 
--- a/libgcc/config/mips/crtn.S
+++ b/libgcc/config/mips/crtn.S
@@ -21,6 +21,9 @@ a copy of the GCC Runtime Library Except
 see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see
 <http://www.gnu.org/licenses/>.  */
 
+/* An executable stack is *not* required for these functions.  */
+	.section .note.GNU-stack,"",%progbits
+
 /* 4 slots for argument spill area.  1 for cpreturn, 1 for stack.
    Return spill offset of 40 and 20.  Aligned to 16 bytes for n32.  */
 
--- a/libgcc/config/mips/mips16.S
+++ b/libgcc/config/mips/mips16.S
@@ -48,6 +48,10 @@ see the files COPYING3 and COPYING.RUNTI
    values using the soft-float calling convention, but do the actual
    operation using the hard floating point instructions.  */
 
+/* An executable stack is *not* required for these functions.  */
+	.section .note.GNU-stack,"",%progbits
+	.previous
+
 #if defined _MIPS_SIM && (_MIPS_SIM == _ABIO32 || _MIPS_SIM == _ABIO64)
 
 /* This file contains 32-bit assembly code.  */
--- a/libgcc/config/mips/vr4120-div.S
+++ b/libgcc/config/mips/vr4120-div.S
@@ -26,6 +26,10 @@ see the files COPYING3 and COPYING.RUNTI
    -mfix-vr4120.  div and ddiv do not give the correct result when one
    of the operands is negative.  */
 
+/* An executable stack is *not* required for these functions.  */
+	.section .note.GNU-stack,"",%progbits
+	.previous
+
 	.set	nomips16
 
 #define DIV								\
