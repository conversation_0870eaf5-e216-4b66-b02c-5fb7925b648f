--- a/configure.py
+++ b/configure.py
@@ -48,7 +48,7 @@ parser = optparse.OptionParser()
 
 valid_os = ('win', 'mac', 'solaris', 'freebsd', 'openbsd', 'linux',
             'android', 'aix', 'cloudabi')
-valid_arch = ('arm', 'arm64', 'ia32', 'mips', 'mipsel', 'mips64el', 'ppc',
+valid_arch = ('arm', 'arm64', 'ia32', 'mips', 'mipsel', 'mips64', 'mips64el', 'ppc',
               'ppc64', 'x32','x64', 'x86', 'x86_64', 's390x')
 valid_arm_float_abi = ('soft', 'softfp', 'hard')
 valid_arm_fpu = ('vfp', 'vfpv3', 'vfpv3-d16', 'neon')
@@ -967,6 +967,9 @@ def host_arch_cc():
   if rtn == 'mipsel' and '_LP64' in k:
     rtn = 'mips64el'
 
+  if rtn == 'mips' and '_LP64' in k:
+    rtn = 'mips64'
+
   return rtn
 
 
@@ -1074,7 +1077,7 @@ def configure_node(o):
 
   if target_arch == 'arm':
     configure_arm(o)
-  elif target_arch in ('mips', 'mipsel', 'mips64el'):
+  elif target_arch in ('mips', 'mipsel', 'mips64', 'mips64el'):
     configure_mips(o, target_arch)
 
   if flavor == 'aix':
