menu "Configuration"

config GOLANG_EXTERNAL_BOOTSTRAP_ROOT
	string "External bootstrap Go root directory"
	default ""
	help
	  Path to a working Go tree (>= Go 1.4), with bin, pkg, and src
	  subdirectories and the Go compiler at bin/go.

	  If specified, the existing Go installation will be used to
	  compile host (buildroot) Go.

	  Leave blank to compile the default bootstrap Go.

config GOLANG_BUILD_CACHE_DIR
	string "Go build cache directory"
	default ""
	help
	  Store the Go build cache in this directory.
	  If not set, uses '$(TMP_DIR)/go-build'.

config GOLANG_MOD_CACHE_WORLD_READABLE
	bool "Ensure Go module cache is world-readable"
	default n

config GOLANG_SPECTRE
	bool "Enable Spectre mitigations"
	default n
	depends on x86_64
	help
	  Currently only available for x86-64 (amd64).

endmenu
