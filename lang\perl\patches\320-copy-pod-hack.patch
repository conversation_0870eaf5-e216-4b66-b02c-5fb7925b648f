--- a/cpan/podlators/Makefile.PL
+++ b/cpan/podlators/Makefile.PL
@@ -18,6 +18,19 @@ use Config;
 use ExtUtils::MakeMaker;
 use File::Spec;
 
+foreach (glob('scripts/pod*.PL')) {
+    # The various pod*.PL extractors change directory. Doing that with relative
+    # paths in @INC breaks. It seems the lesser of two evils to copy (to avoid)
+    # the chdir doing anything, than to attempt to convert lib paths to
+    # absolute, and potentially run into problems with quoting special
+    # characters in the path to our build dir (such as spaces)
+    require File::Copy;
+
+    my $temp = $_;
+    $temp =~ s!scripts/!!;
+    File::Copy::copy($_, $temp) or die "Can't copy $temp to $_: $!";
+}
+
 # Generate full paths for scripts distributed in the bin directory.  Appends
 # the .com extension to scripts on VMS, unless they already have the .PL
 # extension.
@@ -28,7 +41,7 @@ use File::Spec;
 #          (Scalar) Space-separated relative paths from top of distribution
 sub scripts {
     my (@scripts) = @_;
-    my @paths = map { File::Spec->catfile('scripts', $_) } @scripts;
+    my @paths = @scripts;
     if ($^O eq 'VMS') {
         @paths = map { m{ [.] PL \z }xms ? $_ : $_ . '.com' } @paths;
     }
@@ -77,8 +90,8 @@ my %metadata = (
 
     # Override the files that generate section 1 man pages.
     MAN1PODS => {
-        man1pod('scripts', 'pod2man.PL'),
-        man1pod('scripts', 'pod2text.PL'),
+        man1pod('.', 'pod2man.PL'),
+        man1pod('.', 'pod2text.PL'),
 
         # Perl core uses a separate copy in the top-level pod directory.
         ($ENV{PERL_CORE} ? () : man1pod('pod', 'perlpodstyle.pod')),
