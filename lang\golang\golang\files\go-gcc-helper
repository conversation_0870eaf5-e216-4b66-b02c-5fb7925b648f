#!/bin/sh
#
# Copyright (C) 2018, 2020 Jeffery To
#
# This is free software, licensed under the GNU General Public License v2.
# See /LICENSE for more information.
#

me=go-gcc-helper
name="${0##*/}"

log() {
	# shellcheck disable=SC2039
	local IFS=" "
	printf '%s\n' "$me: $*"
}

case "$name" in
gcc)
	if [ -z "$GO_GCC_HELPER_CC" ]; then
		log "missing GO_GCC_HELPER_CC"
		exit 1
	fi
	cmd="$GO_GCC_HELPER_CC"
	;;
g++)
	if [ -z "$GO_GCC_HELPER_CXX" ]; then
		log "missing GO_GCC_HELPER_CXX"
		exit 1
	fi
	cmd="$GO_GCC_HELPER_CXX"
	;;
*)
	log "unknown command \"$name\""
	exit 1
	;;
esac

if [ -n "$GO_GCC_HELPER_PATH" ]; then
	export PATH="$GO_GCC_HELPER_PATH"
else
	log "missing GO_GCC_HELPER_PATH"
fi

log "running $cmd $*"

$cmd "$@"
