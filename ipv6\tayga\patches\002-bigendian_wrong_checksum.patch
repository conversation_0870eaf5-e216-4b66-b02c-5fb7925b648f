--- a/nat64.c
+++ b/nat64.c
@@ -19,6 +19,11 @@
 
 extern struct config *gcfg;
 
+static uint16_t checksum_extend_byte(uint8_t b)
+{
+	return htons(b << 8);
+}
+
 static uint16_t ip_checksum(void *d, int c)
 {
 	uint32_t sum = 0xffff;
@@ -30,7 +35,7 @@ static uint16_t ip_checksum(void *d, int
 	}
 
 	if (c)
-		sum += htons(*((uint8_t *)p) << 8);
+		sum += checksum_extend_byte(*((uint8_t *)p));
 
 	while (sum > 0xffff)
 		sum = (sum & 0xffff) + (sum >> 16);
@@ -180,10 +185,12 @@ static int xlate_payload_4to6(struct pkt
 		cksum = ones_add(p->icmp->cksum, cksum);
 		if (p->icmp->type == 8) {
 			p->icmp->type = 128;
-			p->icmp->cksum = ones_add(cksum, ~(128 - 8));
+			p->icmp->cksum = ones_add(cksum,
+						~checksum_extend_byte(128 - 8));
 		} else {
 			p->icmp->type = 129;
-			p->icmp->cksum = ones_add(cksum, ~(129 - 0));
+			p->icmp->cksum = ones_add(cksum,
+						~checksum_extend_byte(129 - 0));
 		}
 		return 0;
 	case 17:
@@ -668,10 +675,12 @@ static int xlate_payload_6to4(struct pkt
 		cksum = ones_add(p->icmp->cksum, cksum);
 		if (p->icmp->type == 128) {
 			p->icmp->type = 8;
-			p->icmp->cksum = ones_add(cksum, 128 - 8);
+			p->icmp->cksum = ones_add(cksum,
+						checksum_extend_byte(128 - 8));
 		} else {
 			p->icmp->type = 0;
-			p->icmp->cksum = ones_add(cksum, 129 - 0);
+			p->icmp->cksum = ones_add(cksum,
+						checksum_extend_byte(129 - 0));
 		}
 		return 0;
 	case 17:
