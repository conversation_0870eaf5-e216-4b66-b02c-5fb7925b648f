--- a/config
+++ b/config
@@ -1,15 +1,15 @@
 # Installation directories
 
 # Default prefix
-PREFIX ?= /usr/local
+PREFIX ?= /usr
 
 DESTDIR ?= /
 
 # System's libraries directory (where binary libraries are installed)
-LUA_LIBDIR ?= $(PREFIX)/lib/lua/5.1
+LUA_LIBDIR ?= $(PREFIX)/lib/lua
 
 # System's lua directory (where Lua libraries are installed)
-LUA_DIR ?= $(PREFIX)/share/lua/5.1
+LUA_DIR ?= $(PREFIX)/lib/lua
 
 # Lua includes directory
 LUA_INC ?= $(PREFIX)/include
@@ -24,6 +24,5 @@ LIBNAME ?= $T.so.$V
 WARN ?= -O2 -Wall -fPIC -W -Waggregate-return -Wcast-align -Wmissing-prototypes -Wnested-externs -Wshadow -Wwrite-strings -Wpointer-arith -pedantic
 INCS ?= -I$(LUA_INC)
 CFLAGS ?= $(WARN) $(INCS)
-CC ?= gcc
 
 # $Id: config,v 1.7 2007/10/29 22:51:39 carregal Exp $
