# Full configuration can be retrieved from the running
# server at http://localhost:19999/netdata.conf
#
# Example:
#   curl -o /etc/netdata/netdata.conf http://localhost:19999/netdata.conf
#

[global]
	update every = 2
	memory deduplication (ksm) = no
	debug log = syslog
	error log = syslog
	access log = none
	run as user = root

[web]
	allow connections from = localhost 10.* 192.168.* 172.16.* 172.17.* 172.18.* 172.19.* 172.20.* 172.21.* 172.22.* 172.23.* 172.24.* 172.25.* 172.26.* 172.27.* 172.28.* 172.29.* 172.30.* 172.31.*
	allow dashboard from = localhost 10.* 192.168.* 172.16.* 172.17.* 172.18.* 172.19.* 172.20.* 172.21.* 172.22.* 172.23.* 172.24.* 172.25.* 172.26.* 172.27.* 172.28.* 172.29.* 172.30.* 172.31.*

[plugins]
	cgroups = no
	apps = no
	charts.d = yes
	fping = no
	node.d = no
	python.d = no

[health]
	enabled = no

[plugin:proc:ipc]
	shared memory totals = no
