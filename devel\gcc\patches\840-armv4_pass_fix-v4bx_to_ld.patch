commit 7edc8ca5456d9743dd0075eb3cc5b04f4f24c8cc
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Wed Feb 2 19:34:36 2011 +0000

    add armv4 fixup patches
    
    SVN-Revision: 25322


--- a/gcc/config/arm/linux-eabi.h
+++ b/gcc/config/arm/linux-eabi.h
@@ -88,10 +88,15 @@
 #define MUSL_DYNAMIC_LINKER \
   "/lib/ld-musl-arm" MUSL_DYNAMIC_LINKER_E "%{mfloat-abi=hard:hf}.so.1"
 
+/* For armv4 we pass --fix-v4bx to linker to support EABI */
+#undef TARGET_FIX_V4BX_SPEC
+#define TARGET_FIX_V4BX_SPEC " %{mcpu=arm8|mcpu=arm810|mcpu=strongarm*"\
+  "|march=armv4|mcpu=fa526|mcpu=fa626:--fix-v4bx}"
+
 /* At this point, bpabi.h will have clobbered LINK_SPEC.  We want to
    use the GNU/Linux version, not the generic BPABI version.  */
 #undef  LINK_SPEC
-#define LINK_SPEC EABI_LINK_SPEC					\
+#define LINK_SPEC EABI_LINK_SPEC TARGET_FIX_V4BX_SPEC			\
   LINUX_OR_ANDROID_LD (LINUX_TARGET_LINK_SPEC,				\
 		       LINUX_TARGET_LINK_SPEC " " ANDROID_LINK_SPEC)
 
