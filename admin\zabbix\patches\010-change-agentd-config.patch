--- a/conf/zabbix_agentd.conf
+++ b/conf/zabbix_agentd.conf
@@ -3,12 +3,11 @@
 
 ############ GENERAL PARAMETERS #################
 
-### Option: PidFile
-#	Name of PID file.
-#
-# Mandatory: no
-# Default:
-# PidFile=/tmp/zabbix_agentd.pid
+# We do not need PidFile with procd
+# PidFile=/var/run/zabbix_agentd.pid
+
+# use syslog
+LogType=system
 
 ### Option: LogType
 #	Specifies where log messages are written to:
@@ -27,8 +26,6 @@
 # Default:
 # LogFile=
 
-LogFile=/tmp/zabbix_agentd.log
-
 ### Option: LogFileSize
 #	Maximum size of log file in MB.
 #	0 - disable automatic log rotation.
@@ -136,6 +133,7 @@ Server=127.0.0.1
 # Range: 0-100
 # Default:
 # StartAgents=3
+StartAgents=1
 
 ##### Active checks related
 
@@ -164,8 +162,6 @@ Server=127.0.0.1
 # Default:
 # ServerActive=
 
-ServerActive=127.0.0.1
-
 ### Option: Hostname
 #	List of comma delimited unique, case sensitive hostnames.
 #	Required for active checks and must match hostnames as configured on the server.
@@ -175,8 +171,6 @@ ServerActive=127.0.0.1
 # Default:
 # Hostname=
 
-Hostname=Zabbix server
-
 ### Option: HostnameItem
 #	Item used for generating Hostname if it is undefined. Ignored if Hostname is defined.
 #	Does not support UserParameters or aliases.
@@ -326,8 +320,8 @@ Hostname=Zabbix server
 # Include=
 
 # Include=/usr/local/etc/zabbix_agentd.userparams.conf
-# Include=/usr/local/etc/zabbix_agentd.conf.d/
 # Include=/usr/local/etc/zabbix_agentd.conf.d/*.conf
+Include=/etc/zabbix_agentd.conf.d/
 
 ####### USER-DEFINED MONITORED PARAMETERS #######
 
