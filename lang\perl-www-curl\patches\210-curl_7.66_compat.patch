--- a/Curl.xs
+++ b/Curl.xs
@@ -70,7 +70,7 @@ typedef struct {
 
 
 typedef struct {
-#ifdef __CURL_MULTI_H
+#ifdef CURLINC_MULTI_H
     struct CURLM *curlm;
 #else
     struct void *curlm;
@@ -234,7 +234,7 @@ static perl_curl_multi * perl_curl_multi
 {
     perl_curl_multi *self;
     Newz(1, self, 1, perl_curl_multi);
-#ifdef __CURL_MULTI_H
+#ifdef CURLINC_MULTI_H
     self->curlm=curl_multi_init();
 #else
     croak("curl version too old to support curl_multi_init()");
@@ -245,7 +245,7 @@ static perl_curl_multi * perl_curl_multi
 /* delete the multi */
 static void perl_curl_multi_delete(perl_curl_multi *self)
 {
-#ifdef __CURL_MULTI_H
+#ifdef CURLINC_MULTI_H
     if (self->curlm) 
         curl_multi_cleanup(self->curlm);
     Safefree(self);
@@ -1065,7 +1065,7 @@ curl_multi_add_handle(curlm, curl)
     WWW::Curl::Multi curlm
     WWW::Curl::Easy curl
     CODE:
-#ifdef __CURL_MULTI_H
+#ifdef CURLINC_MULTI_H
         curl_multi_add_handle(curlm->curlm, curl->curl);
 #endif
 
@@ -1074,7 +1074,7 @@ curl_multi_remove_handle(curlm, curl)
     WWW::Curl::Multi curlm
     WWW::Curl::Easy curl
     CODE:
-#ifdef __CURL_MULTI_H
+#ifdef CURLINC_MULTI_H
         curl_multi_remove_handle(curlm->curlm, curl->curl);
 #endif
 
@@ -1149,7 +1149,7 @@ curl_multi_perform(self)
     PREINIT:
         int remaining;
     CODE:
-#ifdef __CURL_MULTI_H
+#ifdef CURLINC_MULTI_H
         while(CURLM_CALL_MULTI_PERFORM ==
             curl_multi_perform(self->curlm, &remaining));
 	    RETVAL = remaining;
