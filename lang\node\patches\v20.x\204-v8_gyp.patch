--- a/tools/v8_gypfiles/v8.gyp
+++ b/tools/v8_gypfiles/v8.gyp
@@ -73,6 +73,7 @@
       ],
       'hard_dependency': 1,
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(SHARED_INTERMEDIATE_DIR)',
         ],
@@ -194,6 +195,7 @@
           '<@(torque_outputs_cc)',
           '<@(torque_outputs_inc)',
         ],
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(SHARED_INTERMEDIATE_DIR)',
         ],
@@ -215,6 +217,7 @@
         'sources': [
           '<(generate_bytecode_builtins_list_output)',
         ],
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(generate_bytecode_output_root)',
           '<(SHARED_INTERMEDIATE_DIR)',
@@ -252,6 +255,7 @@
       'sources': [
         '<(V8_ROOT)/src/init/setup-isolate-full.cc',
       ],
+      'include_dirs': [ '<!@(echo "$STAGING_DIR"/usr/../usr/include)' ],
     },  # v8_init
     {
       'target_name': 'v8_initializers',
@@ -263,9 +267,11 @@
         'v8_shared_internal_headers',
         'v8_pch',
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(SHARED_INTERMEDIATE_DIR)',
         '<(generate_bytecode_output_root)',
+        '<!@(echo "$STAGING_DIR"/usr/../usr/include)',
       ],
       'sources': [
         '<!@pymod_do_main(GN-scraper "<(V8_ROOT)/BUILD.gn"  "\\"v8_initializers.*?sources = ")',
@@ -694,6 +700,7 @@
       'toolsets': ['host', 'target'],
       'direct_dependent_settings': {
         'sources': ['<!@pymod_do_main(GN-scraper "<(V8_ROOT)/BUILD.gn"  "v8_compiler_sources = ")'],
+        'include_dirs': [ '<!@(echo "$STAGING_DIR"/usr/../usr/include)' ],
         'conditions': [
           ['v8_target_arch=="ia32"', {
             'sources': [
@@ -802,6 +809,8 @@
       'target_name': 'v8_turboshaft',
       'type': 'static_library',
       'toolsets': ['host', 'target'],
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'include_dirs': [ '<!@(echo "$STAGING_DIR"/usr/../usr/include)' ],
       'dependencies': [
         'generate_bytecode_builtins_list',
         'run_torque',
@@ -826,6 +835,7 @@
         'run_torque',
         'v8_maybe_icu',
       ],
+      'include_dirs': [ '<!@(echo "$STAGING_DIR"/usr/../usr/include)' ],
       'conditions': [
         ['(is_component_build and not v8_optimized_debug and v8_enable_fast_mksnapshot) or v8_enable_turbofan==0', {
           'dependencies': [
@@ -866,6 +876,7 @@
       ],
       'includes': ['inspector.gypi'],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(generate_bytecode_output_root)',
           '<(SHARED_INTERMEDIATE_DIR)',
@@ -1495,6 +1506,7 @@
         }],
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(V8_ROOT)/include',
         ],
@@ -1515,6 +1527,7 @@
     {
       'target_name': 'bytecode_builtins_list_generator',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'conditions': [
         ['want_separate_host_toolset', {
           'toolsets': ['host'],
@@ -1543,6 +1556,9 @@
     {
       'target_name': 'mksnapshot',
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'library_dirs':[ '../../../../staging_dir/hostpkg/share/icu/current/lib' ],
+      'include_dirs': [ '<!@(echo "$STAGING_DIR"/usr/../usr/include)' ],
       'dependencies': [
         'v8_base_without_compiler',
         'v8_compiler_for_mksnapshot',
@@ -1570,6 +1586,7 @@
     {
       'target_name': 'torque',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [
         'torque_base',
         # "build/win:default_exe_manifest",
@@ -1612,6 +1629,7 @@
     {
       'target_name': 'torque-language-server',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'conditions': [
         ['want_separate_host_toolset', {
           'toolsets': ['host'],
@@ -1643,6 +1661,8 @@
     {
       'target_name': 'gen-regexp-special-case',
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'library_dirs':[ '../../../../staging_dir/hostpkg/share/icu/current/lib' ],
       'dependencies': [
         'v8_libbase',
         # "build/win:default_exe_manifest",
@@ -1861,6 +1881,7 @@
          }],
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(V8_ROOT)/include',
         ],
@@ -1982,15 +2003,19 @@
         }],
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(V8_ROOT)/third_party/zlib',
           '<(V8_ROOT)/third_party/zlib/google',
+          '<!@(echo "$STAGING_DIR"/usr/../usr/include)',
         ],
       },
       'defines': [ 'ZLIB_IMPLEMENTATION' ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(V8_ROOT)/third_party/zlib',
         '<(V8_ROOT)/third_party/zlib/google',
+        '<!@(echo "$STAGING_DIR"/usr/../usr/include)',
       ],
       'sources': [
         '<(V8_ROOT)/third_party/zlib/adler32.c',
