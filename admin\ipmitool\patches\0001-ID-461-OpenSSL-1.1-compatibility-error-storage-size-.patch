From b57487e360916ab3eaa50aa6d021c73b6337a4a0 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 30 Nov 2016 17:33:00 +0100
Subject: [PATCH 1/4] ID:461 - OpenSSL 1.1 compatibility - "error: storage size
 of 'ctx' isn't known"

In OpenSSL 1.1 EVP_CIPHER_CTX became opaque, cf. `man 3ssl EVP_EncryptInit`

Fixes: ID:461
---
 src/plugins/lanplus/lanplus_crypt_impl.c | 28 ++++++++++++++--------------
 1 <USER> <GROUP>, 14 insertions(+), 14 deletions(-)

--- a/src/plugins/lanplus/lanplus_crypt_impl.c
+++ b/src/plugins/lanplus/lanplus_crypt_impl.c
@@ -164,10 +164,10 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 							uint8_t       * output,
 							uint32_t        * bytes_written)
 {
-	EVP_CIPHER_CTX ctx;
-	EVP_CIPHER_CTX_init(&ctx);
-	EVP_EncryptInit_ex(&ctx, EVP_aes_128_cbc(), NULL, key, iv);
-	EVP_CIPHER_CTX_set_padding(&ctx, 0);
+	EVP_CIPHER_CTX* ctx;
+	EVP_CIPHER_CTX_init(ctx);
+	EVP_EncryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
+	EVP_CIPHER_CTX_set_padding(ctx, 0);
 	
 
 	*bytes_written = 0;
@@ -191,7 +191,7 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 	assert((input_length % IPMI_CRYPT_AES_CBC_128_BLOCK_SIZE) == 0);
 
 
-	if(!EVP_EncryptUpdate(&ctx, output, (int *)bytes_written, input, input_length))
+	if(!EVP_EncryptUpdate(ctx, output, (int *)bytes_written, input, input_length))
 	{
 		/* Error */
 		*bytes_written = 0;
@@ -201,7 +201,7 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 	{
 		uint32_t tmplen;
 
-		if(!EVP_EncryptFinal_ex(&ctx, output + *bytes_written, (int *)&tmplen))
+		if(!EVP_EncryptFinal_ex(ctx, output + *bytes_written, (int *)&tmplen))
 		{
 			*bytes_written = 0;
 			return; /* Error */
@@ -210,7 +210,7 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 		{
 			/* Success */
 			*bytes_written += tmplen;
-			EVP_CIPHER_CTX_cleanup(&ctx);
+			EVP_CIPHER_CTX_cleanup(ctx);
 		}
 	}
 }
@@ -239,10 +239,10 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 							uint8_t       * output,
 							uint32_t        * bytes_written)
 {
-	EVP_CIPHER_CTX ctx;
-	EVP_CIPHER_CTX_init(&ctx);
-	EVP_DecryptInit_ex(&ctx, EVP_aes_128_cbc(), NULL, key, iv);
-	EVP_CIPHER_CTX_set_padding(&ctx, 0);
+	EVP_CIPHER_CTX* ctx;
+	EVP_CIPHER_CTX_init(ctx);
+	EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
+	EVP_CIPHER_CTX_set_padding(ctx, 0);
 
 
 	if (verbose >= 5)
@@ -266,7 +266,7 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 	assert((input_length % IPMI_CRYPT_AES_CBC_128_BLOCK_SIZE) == 0);
 
 
-	if (!EVP_DecryptUpdate(&ctx, output, (int *)bytes_written, input, input_length))
+	if (!EVP_DecryptUpdate(ctx, output, (int *)bytes_written, input, input_length))
 	{
 		/* Error */
 		lprintf(LOG_DEBUG, "ERROR: decrypt update failed");
@@ -277,7 +277,7 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 	{
 		uint32_t tmplen;
 
-		if (!EVP_DecryptFinal_ex(&ctx, output + *bytes_written, (int *)&tmplen))
+		if (!EVP_DecryptFinal_ex(ctx, output + *bytes_written, (int *)&tmplen))
 		{
 			char buffer[1000];
 			ERR_error_string(ERR_get_error(), buffer);
@@ -290,7 +290,7 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 		{
 			/* Success */
 			*bytes_written += tmplen;
-			EVP_CIPHER_CTX_cleanup(&ctx);
+			EVP_CIPHER_CTX_cleanup(ctx);
 		}
 	}
 
