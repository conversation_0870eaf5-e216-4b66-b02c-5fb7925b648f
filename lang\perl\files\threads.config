($owrt:threads eq 'yes') {
	# Configure arguments
	config_args="$config_args -Dusethreads"
	config_arg2='-Dusethreads'
	config_argc=2
	
	# Options
	useithreads='define'
	usemultiplicity='define'
	usethreads='define'
	
	# Flags and related
	ccflags="-D_REENTRANT -D_GNU_SOURCE $ccflags"
	cppflags="-D_REENTRANT -D_GNU_SOURCE $cppflags"
	perllibs="-lpthread $perllibs"
	libs="-lpthread $libs"
	libsfiles="libpthread.so $libsfiles"
	libswanted="pthread $libswanted"
	
	
	crypt_r_proto='REENTRANT_PROTO_B_CCS'
	i_crypt='define'
	d_crypt='define'
	d_crypt_r='define'
	
	# Prototypes
	asctime_r_proto='REENTRANT_PROTO_B_SB'
	ctime_r_proto='REENTRANT_PROTO_B_SB'
	drand48_r_proto='REENTRANT_PROTO_I_ST'
	getgrent_r_proto='REENTRANT_PROTO_I_SBWR'
	getgrgid_r_proto='REENTRANT_PROTO_I_TSBWR'
	getgrnam_r_proto='REENTRANT_PROTO_I_CSBWR'
	gethostbyaddr_r_proto='REENTRANT_PROTO_I_TsISBWRE'
	gethostbyname_r_proto='REENTRANT_PROTO_I_CSBWRE'
	gethostent_r_proto='REENTRANT_PROTO_I_SBWRE'
	getlogin_r_proto='REENTRANT_PROTO_I_BW'
	getnetbyaddr_r_proto='REENTRANT_PROTO_I_uISBWRE'
	getnetbyname_r_proto='REENTRANT_PROTO_I_CSBWRE'
	getnetent_r_proto='REENTRANT_PROTO_I_SBWRE'
	getprotobyname_r_proto='REENTRANT_PROTO_I_CSBWR'
	getprotobynumber_r_proto='REENTRANT_PROTO_I_ISBWR'
	getprotoent_r_proto='REENTRANT_PROTO_I_SBWR'
	getpwent_r_proto='REENTRANT_PROTO_I_SBWR'
	getpwnam_r_proto='REENTRANT_PROTO_I_CSBWR'
	getpwuid_r_proto='REENTRANT_PROTO_I_TSBWR'
	getservbyname_r_proto='REENTRANT_PROTO_I_CCSBWR'
	getservbyport_r_proto='REENTRANT_PROTO_I_ICSBWR'
	getservent_r_proto='REENTRANT_PROTO_I_SBWR'
	getspnam_r_proto='REENTRANT_PROTO_I_CSBWR'
	gmtime_r_proto='REENTRANT_PROTO_S_TS'
	localtime_r_proto='REENTRANT_PROTO_S_TS'
	random_r_proto='REENTRANT_PROTO_I_St'
	readdir64_r_proto='REENTRANT_PROTO_I_TSR'
	readdir_r_proto='REENTRANT_PROTO_I_TSR'
	srand48_r_proto='REENTRANT_PROTO_I_LS'
	srandom_r_proto='REENTRANT_PROTO_I_TS'
	strerror_r_proto='REENTRANT_PROTO_B_IBW'
	tmpnam_r_proto='REENTRANT_PROTO_B_B'
	ttyname_r_proto='REENTRANT_PROTO_I_IBW'
	
	# Defines
	d_asctime_r='define'
	d_ctime_r='define'
	d_drand48_r='define'
	d_fds_bits='define'
	d_fegetround='undef'
	d_getgrent_r='define'
	d_getgrgid_r='define'
	d_getgrnam_r='define'
	d_gethostbyaddr_r='define'
	d_gethostbyname_r='define'
	d_gethostent_r='define'
	d_getlogin_r='define'
	d_getnetbyaddr_r='define'
	d_getnetbyname_r='define'
	d_getnetent_r='define'
	d_getprotobyname_r='define'
	d_getprotobynumber_r='define'
	d_getprotoent_r='define'
	d_getpwent_r='define'
	d_getpwnam_r='define'
	d_getpwuid_r='define'
	d_getservbyname_r='define'
	d_getservbyport_r='define'
	d_getservent_r='define'
	d_getspnam_r='define'
	d_gmtime_r='define'
	d_j0='undef'
	d_j0l='undef'
	d_localtime_r='define'
	d_localtime_r_needs_tzset='define'
	d_nexttoward='undef'
	d_off64_t='define'
	d_pthread_atfork='define'
	d_pthread_yield='define'
	d_random_r='define'
	d_readdir64_r='define'
	d_readdir_r='define'
	d_srand48_r='define'
	d_srandom_r='define'
	d_sresgproto='define'
	d_sresuproto='define'
	d_strerror_r='define'
	d_tmpnam_r='define'
	d_ttyname_r='define'
}
($owrt:threads eq 'no') {
	# Options
	useithreads='undef'
	usemultiplicity='undef'
	usethreads='undef'
	
	# Prototyypes
	asctime_r_proto='0'
	crypt_r_proto='0'
	ctime_r_proto='0'
	drand48_r_proto='0'
	getgrent_r_proto='0'
	getgrgid_r_proto='0'
	getgrnam_r_proto='0'
	gethostbyaddr_r_proto='0'
	gethostbyname_r_proto='0'
	gethostent_r_proto='0'
	getlogin_r_proto='0'
	getnetbyaddr_r_proto='0'
	getnetbyname_r_proto='0'
	getnetent_r_proto='0'
	getprotobyname_r_proto='0'
	getprotobynumber_r_proto='0'
	getprotoent_r_proto='0'
	getpwent_r_proto='0'
	getpwnam_r_proto='0'
	getpwuid_r_proto='0'
	getservbyname_r_proto='0'
	getservbyport_r_proto='0'
	getservent_r_proto='0'
	getspnam_r_proto='0'
	gmtime_r_proto='0'
	
	# Defines
	d_asctime_r='undef'
	d_crypt='define'
	d_crypt_r='undef'
	d_ctime_r='undef'
	d_drand48_r='undef'
	d_fds_bits='undef'
	d_fegetround='define'
	d_getgrent_r='undef'
	d_getgrgid_r='undef'
	d_getgrnam_r='undef'
	d_gethostbyaddr_r='undef'
	d_gethostbyname_r='undef'
	d_gethostent_r='undef'
	d_getlogin_r='undef'
	d_getnetbyaddr_r='undef'
	d_getnetbyname_r='undef'
	d_getnetent_r='undef'
	d_getprotobyname_r='undef'
	d_getprotobynumber_r='undef'
	d_getprotoent_r='undef'
	d_getpwent_r='undef'
	d_getpwnam_r='undef'
	d_getpwuid_r='undef'
	d_getservbyname_r='undef'
	d_getservbyport_r='undef'
	d_getservent_r='undef'
	d_getspnam_r='undef'
	d_gmtime_r='undef'
	d_j0='define'
	d_j0l='define'
	d_localtime_r='undef'
	d_localtime_r_needs_tzset='undef'
	d_nexttoward='define'
	d_off64_t='undef'
	d_pthread_atfork='undef'
	d_pthread_yield='undef'
	d_random_r='undef'
	d_readdir64_r='undef'
	d_readdir_r='undef'
	d_srand48_r='undef'
	d_srandom_r='undef'
	d_sresgproto='undef'
	d_sresuproto='undef'
	d_strerror_r='undef'
	d_tmpnam_r='undef'
	d_ttyname_r='undef'
	i_crypt='define'
	localtime_r_proto='0'
	random_r_proto='0'
	readdir64_r_proto='0'
	readdir_r_proto='0'
	srand48_r_proto='0'
	srandom_r_proto='0'
	strerror_r_proto='0'
	tmpnam_r_proto='0'
	ttyname_r_proto='0'
}
