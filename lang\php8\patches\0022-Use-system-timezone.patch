From: Debian PHP Maintainers <<EMAIL>>
Date: Sat, 2 May 2015 10:26:56 +0200
Subject: Use system timezone

Upstream don't want this patch. See
http://bugs.debian.org/cgi-bin/bugreport.cgi?bug=730771 for a summary.

This delta is recovered from previous versions of the system timezone patch in
Debian, and appears to have inadvertently been dropped. Author unknown.

To be used in tandem with use_embedded_timezonedb.patch and use_embedded_timezonedb_fixes.patch.
---
 ext/date/php_date.c | 17 +++++++++++++++++
 1 file changed, 17 insertions(+)

--- a/ext/date/php_date.c
+++ b/ext/date/php_date.c
@@ -568,6 +568,23 @@ static const char* guess_timezone(const
 	} else if (*DATEG(default_timezone)) {
 		return DATEG(default_timezone);
 	}
+	/* Try to guess timezone from system information */
+	{
+		struct tm *ta, tmbuf;
+		time_t     the_time;
+		char      *tzid = NULL;
+
+		the_time = time(NULL);
+		ta = php_localtime_r(&the_time, &tmbuf);
+		if (ta) {
+			tzid = timelib_timezone_id_from_abbr(ta->tm_zone, ta->tm_gmtoff, ta->tm_isdst);
+		}
+		if (! tzid) {
+			tzid = "UTC";
+		}
+
+		return tzid;
+	}
 	/* Fallback to UTC */
 	return "UTC";
 }
