commit 64661de100da1ec1061ef3e5e400285dce115e6b
Author: <PERSON> <<EMAIL>>
Date:   Sun May 10 13:16:35 2015 +0000

    gcc: add some size optimization patches
    
    Signed-off-by: <PERSON> <<EMAIL>>
    
    SVN-Revision: 45664

--- a/libgcc/config/t-libunwind
+++ b/libgcc/config/t-libunwind
@@ -2,8 +2,7 @@
 
 HOST_LIBGCC2_CFLAGS += -DUSE_GAS_SYMVER
 
-LIB2ADDEH = $(srcdir)/unwind-sjlj.c $(srcdir)/unwind-c.c \
-  $(srcdir)/unwind-compat.c $(srcdir)/unwind-dw2-fde-compat.c
+LIB2ADDEH = $(srcdir)/unwind-sjlj.c $(srcdir)/unwind-c.c
 LIB2ADDEHSTATIC = $(srcdir)/unwind-sjlj.c $(srcdir)/unwind-c.c
 
 # Override the default value from t-slibgcc-elf-ver and mention -lunwind
