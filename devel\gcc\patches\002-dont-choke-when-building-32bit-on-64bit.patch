--- a/gcc/real.h
+++ b/gcc/real.h
@@ -70,9 +70,10 @@ struct GTY(()) real_value {
    + (REAL_VALUE_TYPE_SIZE%HOST_BITS_PER_WIDE_INT ? 1 : 0)) /* round up */
 
 /* Verify the guess.  */
+#ifndef __LP64__
 extern char test_real_width
   [sizeof (REAL_VALUE_TYPE) <= REAL_WIDTH * sizeof (HOST_WIDE_INT) ? 1 : -1];
-
+#endif
 /* Calculate the format for CONST_DOUBLE.  We need as many slots as
    are necessary to overlay a REAL_VALUE_TYPE on them.  This could be
    as many as four (32-bit HOST_WIDE_INT, 128-bit REAL_VALUE_TYPE).
