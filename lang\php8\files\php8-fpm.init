#!/bin/sh /etc/rc.common

START=50

PROG=/usr/bin/php8-fpm
CONFIG=/etc/php8-fpm.conf

SERVICE_PID_FILE=/var/run/php8-fpm.pid

start_instance() {
	local section="$1"
	local enabled

	config_get_bool enabled "$section" 'enabled' 0

	[ $enabled -gt 0 ] || return 1

	service_start $PROG -y $CONFIG -g $SERVICE_PID_FILE
}

start() {
	config_load 'php8-fpm'
	config_foreach start_instance 'php8-fpm'
}

stop() {
	service_stop $PROG
}
