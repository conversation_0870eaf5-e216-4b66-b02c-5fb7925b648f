menu "Configuration"
	depends on PACKAGE_perl

config PERL_THREADS
    bool "Enable threading support"
    default y if (mips || mipsel || i386 || i686 || x86_64 || armeb || arm || aarch64)
    default n

config PERL_TESTS
	bool "Include perl tests"
	default n
	help
		Include test suites for all perl packages.
		
		This will increase the size of perl and related packages
		considerably.
		Test support is still in development. Some tests will fail,
		others are just missing completely.

config PERL_NOCOMMENT
	bool "Strip comments and pod sections from modules"
	default y
	help
		Remove comments and pod sections for all perl packages.

		This will descrease the size of perl libraries moderately.

		Stripping occasionally gets confused and mangles valid code,
		so disable this option if you're not pressed for space.

endmenu
