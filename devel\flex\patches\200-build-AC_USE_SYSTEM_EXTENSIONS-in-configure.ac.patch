From 24fd0551333e7eded87b64dd36062da3df2f6380 Mon Sep 17 00:00:00 2001
From: Explorer09 <<EMAIL>>
Date: Mon, 4 Sep 2017 10:47:33 +0800
Subject: [PATCH] build: AC_USE_SYSTEM_EXTENSIONS in configure.ac.

This would, e.g. define _GNU_SOURCE in config.h, enabling the
reallocarray() prototype in glibc 2.26+ on Linux systems with that
version of glibc.

Fixes #241.
---
 configure.ac | 2 ++
 1 file changed, 2 insertions(+)

--- a/configure.ac
+++ b/configure.ac
@@ -25,8 +25,10 @@
 # autoconf requirements and initialization
 
 AC_INIT([the fast lexical analyser generator],[2.6.4],[<EMAIL>],[flex])
+AC_PREREQ([2.60])
 AC_CONFIG_SRCDIR([src/scan.l])
 AC_CONFIG_AUX_DIR([build-aux])
+AC_USE_SYSTEM_EXTENSIONS
 LT_INIT
 AM_INIT_AUTOMAKE([1.11.3 -Wno-portability foreign check-news std-options dist-lzip parallel-tests subdir-objects])
 AC_CONFIG_HEADER([src/config.h])
