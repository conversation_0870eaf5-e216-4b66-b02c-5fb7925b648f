--- a/libteredo/debug.h
+++ b/libteredo/debug.h
@@ -43,8 +43,10 @@ static inline void debug (const char *st
 #  ifdef __linux__
 #   include <errno.h>
 #   include <assert.h>
-#   undef PTHREAD_MUTEX_INITIALIZER
-#   define PTHREAD_MUTEX_INITIALIZER PTHREAD_ERRORCHECK_MUTEX_INITIALIZER_NP
+#   if defined(PTHREAD_ERRORCHECK_MUTEX_INITIALIZER_NP)
+#     undef PTHREAD_MUTEX_INITIALIZER
+#     define PTHREAD_MUTEX_INITIALIZER PTHREAD_ERRORCHECK_MUTEX_INITIALIZER_NP
+#   endif
 
 static inline int
 d_pthread_mutex_init (pthread_mutex_t *mutex, pthread_mutexattr_t *pattr)
@@ -57,7 +59,7 @@ d_pthread_mutex_init (pthread_mutex_t *m
 		pthread_mutexattr_init (&attr);
 	}
 
-	pthread_mutexattr_settype (pattr, PTHREAD_MUTEX_ERRORCHECK_NP);
+	pthread_mutexattr_settype (pattr, PTHREAD_MUTEX_ERRORCHECK);
 	int res = pthread_mutex_init (mutex, pattr);
 
 	if (pattr == &attr)
