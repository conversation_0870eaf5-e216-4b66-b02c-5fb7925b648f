Description: Ignore files we do not want installed.
Forwarded: not-needed
Author: <PERSON> <<EMAIL>>
Reviewed-by: gregor herrmann <<EMAIL>>
Last-Update: 2013-10-28

--- a/Makefile.PL
+++ b/Makefile.PL
@@ -2,6 +2,11 @@ use strict;
 use warnings;
 use Inline::MakeMaker;
 
+sub MY::libscan {
+	return if ($_[1] eq 'USB.pm' or $_[1] eq 'dump_usb.pl');
+	return $_[1];
+}
+
 if($^O eq 'MSWin32')
 {
     if(!$ENV{LIBUSB_LIBDIR} or !$ENV{LIBUSB_INCDIR})
