From 3fcd042d26d70856e826a42b5f93dc4854d80bf0 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 6 Apr 2018 19:36:15 +0200
Subject: Invoke ed directly instead of using the shell

* src/pch.c (do_ed_script): Invoke ed directly instead of using a shell
command to avoid quoting vulnerabilities.
---
 src/pch.c | 6 ++----
 1 file changed, 2 insertions(+), 4 deletions(-)

--- a/src/pch.c
+++ b/src/pch.c
@@ -2459,9 +2459,6 @@ do_ed_script (char const *inname, char c
 	    *outname_needs_removal = true;
 	    copy_file (inname, outname, 0, exclusive, instat.st_mode, true);
 	  }
-	sprintf (buf, "%s %s%s", editor_program,
-		 verbosity == VERBOSE ? "" : "- ",
-		 outname);
 	fflush (stdout);
 
 	pid = fork();
@@ -2470,7 +2467,8 @@ do_ed_script (char const *inname, char c
 	else if (pid == 0)
 	  {
 	    dup2 (tmpfd, 0);
-	    execl ("/bin/sh", "sh", "-c", buf, (char *) 0);
+	    assert (outname[0] != '!' && outname[0] != '-');
+	    execlp (editor_program, editor_program, "-", outname, (char  *) NULL);
 	    _exit (2);
 	  }
 	else
