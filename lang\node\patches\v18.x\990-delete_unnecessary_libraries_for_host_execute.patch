--- a/tools/icu/icu-generic.gyp
+++ b/tools/icu/icu-generic.gyp
@@ -418,6 +418,7 @@
       'target_name': 'genrb',
       'type': 'executable',
       'toolsets': [ 'host' ],
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools', 'icu_implementation' ],
       'sources': [
         '<@(icu_src_genrb)'
@@ -434,6 +435,7 @@
       'target_name': 'iculslocs',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         'iculslocs.cc',
@@ -446,6 +448,7 @@
       'target_name': 'icupkg',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_icupkg)',
@@ -457,6 +460,7 @@
       'target_name': 'genccode',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_genccode)',
--- a/tools/v8_gypfiles/v8.gyp
+++ b/tools/v8_gypfiles/v8.gyp
@@ -1397,6 +1397,7 @@
     {
       'target_name': 'bytecode_builtins_list_generator',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'conditions': [
         ['want_separate_host_toolset', {
           'toolsets': ['host'],
@@ -1421,6 +1422,8 @@
     {
       'target_name': 'mksnapshot',
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'library_dirs':[ '../../../../staging_dir/hostpkg/share/icu/current/lib' ],
       'dependencies': [
         'v8_base_without_compiler',
         'v8_compiler_for_mksnapshot',
@@ -1450,6 +1453,7 @@
     {
       'target_name': 'torque',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [
         'torque_base',
         # "build/win:default_exe_manifest",
@@ -1488,6 +1492,7 @@
     {
       'target_name': 'torque-language-server',
       'type': 'executable',
+      'libraries!':[ '-licui18n', '-licuuc', '-licudata', '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'conditions': [
         ['want_separate_host_toolset', {
           'toolsets': ['host'],
@@ -1515,6 +1520,8 @@
     {
       'target_name': 'gen-regexp-special-case',
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
+      'library_dirs':[ '../../../../staging_dir/hostpkg/share/icu/current/lib' ],
       'dependencies': [
         'v8_libbase',
         # "build/win:default_exe_manifest",
