# gcc library configuration
menu "Libraries"
	depends on PACKAGE_gcc
	config INCLUDE_STATIC_LIBC
		bool "Include static libc on target"
		help
			Copies libc.a to target device
			Increases the size of an already
			very large package
		default n
	config INCLUDE_STATIC_LIBPTHREAD
		bool "Include static libptread on target"
		help
			Copies libpthread.a to target device
			Increases the size of an already
			very large package
		default n
	config INCLUDE_STATIC_LIBSTDC
		bool "Include static libstdc++ on target"
		help
			Copies libstdc++.a to target device
			Increases the size of an already
			very large package
		default n
	config INCLUDE_STATIC_LINK_SPEC
		depends on INCLUDE_STATIC_LIBSTDC
		bool "Generate spec file for easy static c++ linking on target"
		help
			Creates a spec file for gcc to eliminate the need for
			-lstdc++, libgcc_pic and -static-libstdc flags when
			statically linking c++ programs
		default n
endmenu

