From cdb02b95ed9b12f82620ae1652cba20464c2ccf0 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 1 Nov 2017 14:51:43 +0100
Subject: [PATCH] npm: overriding unsafe-perm setting to install packages
 globally

---
 deps/npm/lib/config/defaults.js | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/deps/npm/lib/config/defaults.js
+++ b/deps/npm/lib/config/defaults.js
@@ -243,7 +243,7 @@ Object.defineProperty(exports, 'defaults
                        process.getgid && process.setgid) ||
                      process.getuid() !== 0,
     'update-notifier': true,
-    usage: false,
+    usage: true,
     user: (process.platform === 'win32' || os.type() === 'OS400') ? 0 : 'nobody',
     userconfig: path.resolve(home, '.npmrc'),
     umask: process.umask ? process.umask() : umask.fromString('022'),
