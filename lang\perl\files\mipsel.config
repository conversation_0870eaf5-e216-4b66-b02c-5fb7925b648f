owrt:arch=mipsel
owrt:bits=32
owrt:endian=little

ccsymbols=''
cppccsymbols=''
cppsymbols='__ELF__=1 _FILE_OFFSET_BITS=64 __GLIBC__=2 __GLIBC_MINOR__=2 __GNUC__=3 __GNUC_MINOR__=4 __GNU_LIBRARY__=6 LANGUAGE_C=1 _LANGUAGE_C=1 __LANGUAGE_C=1 __LANGUAGE_C__=1 _LARGEFILE_SOURCE=1 MIPSEL=1 _MIPSEL=1 __MIPSEL=1 __MIPSEL__=1 _MIPS_FPSET=16 _MIPS_ISA=_MIPS_ISA_MIPS1 _MIPS_SIM=1 _MIPS_SZINT=32 _MIPS_SZLONG=32 _MIPS_SZPTR=32 __PIC__=1 _POSIX_C_SOURCE=199506L _POSIX_SOURCE=1 R3000=1 _R3000=1 __R3000=1 __R3000__=1 __STDC__=1 __USE_BSD=1 __USE_FILE_OFFSET64=1 __USE_LARGEFILE=1 __USE_MISC=1 __USE_POSIX=1 __USE_POSIX199309=1 __USE_POSIX199506=1 __USE_POSIX2=1 __USE_SVID=1 linux=1 __linux=1 __linux__=1 mips=1 _mips=1 __mips=1 __mips__=1 __pic__=1 unix=1 __unix=1 __unix__=1'
d_casti32='define'
d_double_style_ieee='define'
d_modflproto='undef'
doublekind='3'
fpossize='24'
longdblkind='0'
need_va_copy='undef'
quadkind='3'

owrt:sig_count=128
owrt:sigs='ZERO HUP INT QUIT ILL TRAP ABRT EMT FPE KILL BUS SEGV SYS PIPE ALRM TERM USR1 USR2 CHLD PWR WINCH URG IO STOP TSTP CONT TTIN TTOU VTALRM PROF XCPU XFSZ'
owrt:sig_name_extra='IOT CLD POLL'
owrt:sig_num_extra='6 18 22'
