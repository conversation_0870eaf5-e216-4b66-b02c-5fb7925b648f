alignbytes='8'

($owrt:bits eq '32') {
	($owrt:endian eq 'little') {
		byteorder='1234'
	}
	($owrt:endian eq 'big') {
		byteorder='4321'
	}
	
	# Types
	ivsize='4'
	uvsize='4'
	longsize='4'
	longdblsize='8'
	ptrsize='4'
	
	#quadkind='3'
	#doublekind='3'
	#longdblkind='0'
	
	i8type='signed char'
	i16type='signed short'
	i32type='signed long'
	i64type='signed long long'
	u32type='unsigned long'
	u64type='unsigned long long'
	quadtype='long long'
	uquadtype='unsigned long long'
	
	sizesize='4'
	
	use64bitall='undef'
	use64bitint='undef'
	
	uidformat='"lu"'
	gidformat='"lu"'
	
	selectminbits='32'
	
	sGMTIME_max='2147483647'
	sGMTIME_min='-2147483648'
	sLOCALTIME_max='2147483647'
	sLOCALTIME_min='-2147483648'
	
	sPRIi64='"Li"'
	sPRIo64='"Lo"'
	sPRIu64='"Lu"'
	sPRIx64='"Lx"'
	sPRId64='"Ld"'
	
	netdb_host_type='const void *'
	nv_preserves_uv_bits='32'
	d_nv_preserves_uv='define'
	d_printf_format_null='define'
	d_u32align='undef'
}
($owrt:bits eq '64') {
	($owrt:endian eq 'little') {
		byteorder='12345678'
	}
	($owrt:endian eq 'big') {
		byteorder='87654321'
	}
	
	# Types
	ivsize='8'
	uvsize='8'
	longsize='8'
	longdblsize='16'
	ptrsize='8'
	
	quadtype='long'
	
	i16type='signed short'
	i32type='signed int'
	i64type='signed long'
	i8type='signed char'
	
	u32type='unsigned int'
	u64type='unsigned long'
	uquadtype='unsigned long'
	
	sizesize='8'
	
	uidformat='"u"'
	gidformat='"u"'
	
	selectminbits='64'
	
	sGMTIME_max='67768036191676799'
	sGMTIME_min='-62167219200'
	sLOCALTIME_max='67768036191673199'
	sLOCALTIME_min='-62167222408'
	
	sPRIi64='"li"'
	sPRIo64='"lo"'
	sPRIu64='"lu"'
	sPRIx64='"lx"'
	sPRId64='"ld"'
	
	netdb_host_type='char *'
	nv_preserves_uv_bits='53'
	d_nv_preserves_uv='undef'
	d_printf_format_null='undef'
	d_u32align='define'
	use64bitall='define'
	use64bitint='define'
}

# Only defined by i486 and x86_64. Provide defaults for all others
(!defined($sPRIEUldbl)) {
	sPRIEUldbl='"E"'
	sPRIFUldbl='"F"'
	sPRIGUldbl='"G"'
	sPRIXU64='"LX"'
	sPRIeldbl='"e"'
	sPRIfldbl='"f"'
	sPRIgldbl='"g"'
	sSCNfldbl='"f"'
}
