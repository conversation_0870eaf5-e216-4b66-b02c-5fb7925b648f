From c3939dac2c060651361fc71516806f9ab8c38901 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <v<PERSON><PERSON><PERSON>@redhat.com>
Date: Thu, 23 Jan 2020 11:26:32 +0100
Subject: [PATCH] hpmfwupg: move variable definition to .c file

Signed-off-by: <PERSON><PERSON><PERSON> <vdo<PERSON><PERSON>@redhat.com>
---
 include/ipmitool/ipmi_hpmfwupg.h | 2 +-
 lib/ipmi_hpmfwupg.c              | 2 ++
 2 files changed, 3 insertions(+), 1 deletion(-)

--- a/include/ipmitool/ipmi_hpmfwupg.h
+++ b/include/ipmitool/ipmi_hpmfwupg.h
@@ -800,7 +800,7 @@ typedef struct _VERSIONINFO {
 	char descString[HPMFWUPG_DESC_STRING_LENGTH + 1];
 }VERSIONINFO, *PVERSIONINFO;
 
-VERSIONINFO gVersionInfo[HPMFWUPG_COMPONENT_ID_MAX];
+extern VERSIONINFO gVersionInfo[HPMFWUPG_COMPONENT_ID_MAX];
 
 #define TARGET_VER (0x01)
 #define ROLLBACK_VER (0x02)
--- a/lib/ipmi_hpmfwupg.c
+++ b/lib/ipmi_hpmfwupg.c
@@ -58,6 +58,8 @@ ipmi_intf_get_max_request_data_size(stru
 
 extern int verbose;
 
+VERSIONINFO gVersionInfo[HPMFWUPG_COMPONENT_ID_MAX];
+
 int HpmfwupgUpgrade(struct ipmi_intf *intf, char *imageFilename,
 		int activate, int, int);
 int HpmfwupgValidateImageIntegrity(struct HpmfwupgUpgradeCtx *pFwupgCtx);
