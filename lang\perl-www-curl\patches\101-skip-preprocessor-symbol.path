From 0be0223422e6e5f4091c6e4e058d213623eed105 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Petr=20P=C3=ADsa=C5=99?= <<EMAIL>>
Date: Mon, 12 Sep 2016 14:40:44 +0200
Subject: [PATCH] Skip preprocessor symbol only CURL_STRICTER
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

CURL_STRICTER leaked into curl-constants.c when building against
curl-7.50.2. This is a preprocessor only macro without a value.

CPAN RT#117793

Signed-off-by: <PERSON><PERSON> <<EMAIL>>
---
 Makefile.PL | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/Makefile.PL
+++ b/Makefile.PL
@@ -127,7 +127,7 @@ if (!defined($curl_h)) {
     close H;
 
     for my $e (sort @syms) {
-       if($e =~ /(OBSOLETE|^CURL_EXTERN|_LAST\z|_LASTENTRY\z)/) {
+       if($e =~ /(OBSOLETE|^CURL_EXTERN|^CURL_STRICTER\z|_LAST\z|_LASTENTRY\z)/) {
           next;
        }
        my ($group) = $e =~ m/^([^_]+_)/;
