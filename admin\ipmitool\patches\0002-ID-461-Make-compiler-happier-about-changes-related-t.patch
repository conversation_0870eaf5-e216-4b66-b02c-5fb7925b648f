From 77fe5635037ebaf411cae46cf5045ca819b5c145 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Sun, 15 Jan 2017 15:11:25 +0100
Subject: [PATCH 2/4] ID:461 - Make compiler happier about changes related to
 OpenSSL 1.1

Complaint was that ctx isn't initialized.
---
 src/plugins/lanplus/lanplus_crypt_impl.c | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

--- a/src/plugins/lanplus/lanplus_crypt_impl.c
+++ b/src/plugins/lanplus/lanplus_crypt_impl.c
@@ -164,7 +164,7 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 							uint8_t       * output,
 							uint32_t        * bytes_written)
 {
-	EVP_CIPHER_CTX* ctx;
+	EVP_CIPHER_CTX *ctx = NULL;
 	EVP_CIPHER_CTX_init(ctx);
 	EVP_EncryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
 	EVP_CIPHER_CTX_set_padding(ctx, 0);
@@ -239,7 +239,7 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 							uint8_t       * output,
 							uint32_t        * bytes_written)
 {
-	EVP_CIPHER_CTX* ctx;
+	EVP_CIPHER_CTX *ctx = NULL;
 	EVP_CIPHER_CTX_init(ctx);
 	EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
 	EVP_CIPHER_CTX_set_padding(ctx, 0);
