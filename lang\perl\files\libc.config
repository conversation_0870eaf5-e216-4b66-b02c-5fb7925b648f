($owrt:libc eq 'glibc') {
	perllibs="$perllibs -lbsd"
	ldflags="$ldflags -L$owrt:staging_dir/lib"
}

# uclibc does not provide crypt_r().
($owrt:libc eq 'uclibc') {
	crypt_r_proto='0'
	d_crypt_r='undef'
}

($owrt:libc eq 'musl') {
	# musl does not provide a working setlocale(). It accepts arbitrary locales
	# and makes them act as if they were C.UTF-8.
	d_setlocale='undef'
	
	d_stdio_ptr_lval='undef'
	d_stdio_ptr_lval_sets_cnt='undef'
	d_stdiobase='undef'
	d_stdstdio='undef'
	
	d_getnetbyname_r='undef'
	
	d_getprotobyname_r='undef'
	d_getpwent_r='undef'
	d_getservent_r='undef'
	d_gethostent_r='undef'
	d_getnetent_r='undef'
	d_getnetbyaddr_r='undef'
	d_getprotoent_r='undef'
	d_getprotobynumber_r='undef'
	d_getgrent_r='undef'
	
	getprotobyname_r='undef'
	getpwent_r='undef'
	getservent_r='undef'
	gethostent_r='undef'
	getnetent_r='undef'
	getnetbyaddr_r='undef'
	getprotoent_r='undef'
	getprotobynumber_r='undef'
	getgrent_r='undef'
	
	i_fcntl='define'
	h_fcntl='true'
	d_strerror_r='undef'
}
