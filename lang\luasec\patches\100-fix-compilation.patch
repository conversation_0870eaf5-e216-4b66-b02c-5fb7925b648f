--- a/src/Makefile
+++ b/src/Makefile
@@ -16,8 +16,8 @@ WARN=-Wall -pedantic
 BSD_CFLAGS=-O2 -fPIC $(WARN) $(INCDIR) $(DEFS)
 BSD_LDFLAGS=-O -fPIC -shared $(LIBDIR)
 
-LNX_CFLAGS=-O2 -fPIC $(WARN) $(INCDIR) $(DEFS)
-LNX_LDFLAGS=-O -fPIC -shared $(LIBDIR)
+LNX_CFLAGS=$(INCDIR) $(DEFS)
+LNX_LDFLAGS=-shared $(LIBDIR)
 
 MAC_ENV=env MACOSX_DEPLOYMENT_TARGET='$(MACVER)'
 MAC_CFLAGS=-O2 -fno-common $(WARN) $(INCDIR) $(DEFS)
@@ -34,10 +34,10 @@ LDFLAGS += $(MYLDFLAGS)
 all:
 
 install: $(CMOD) $(LMOD)
-	$(INSTALL) -d $(DESTDIR)$(LUAPATH)/ssl $(DESTDIR)$(LUACPATH)
-	$(INSTALL) $(CMOD) $(DESTDIR)$(LUACPATH)
-	$(INSTALL) -m644 $(LMOD) $(DESTDIR)$(LUAPATH)
-	$(INSTALL) -m644 https.lua $(DESTDIR)$(LUAPATH)/ssl
+	$(INSTALL) -d $(LUAPATH)/ssl $(LUACPATH)
+	$(INSTALL) $(CMOD) $(LUACPATH)
+	$(INSTALL) -m644 $(LMOD) $(LUAPATH)
+	$(INSTALL) -m644 https.lua $(LUAPATH)/ssl
 
 linux:
 	@$(MAKE) $(CMOD) MYCFLAGS="$(LNX_CFLAGS)" MYLDFLAGS="$(LNX_LDFLAGS)" EXTRA="$(EXTRA)"
