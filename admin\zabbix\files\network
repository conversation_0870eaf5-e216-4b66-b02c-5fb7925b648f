#see https://openwrt.org/docs/guide-user/services/network_monitoring/zabbix for ready to use templates

# network interface discovery
# example: {"data":[{"{#IF}":"lo", "{#NET}":"loopback"},{"{#IF}":"br-lan", "{#NET}":"lan"},{"{#IF}":"eth0.1", "{#NET}":"wan"}]}
#
UserParameter=netowrt.discovery,lua -l ubus -e 'u=ubus.connect();list="{\"data\":[";dump=u:call("network.interface", "dump", {});for _, intf in ipairs(dump.interface) do list=list.."{\"{#IF}\":\""..(intf.device or intf.l3_device).."\", \"{#NET}\":\""..intf.interface.."\"},";end;list=string.gsub(list,",$","");print(list.."]}")'
