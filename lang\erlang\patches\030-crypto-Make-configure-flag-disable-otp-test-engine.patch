From ca2fed6c60039003f255ffe7a5d98744b8ebdedc Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 17 Feb 2022 12:22:06 +0100
Subject: [PATCH] crypto: Make configure flag --disable-otp-test-engine

---
 lib/crypto/c_src/Makefile.in | 16 ++++++++++++++--
 lib/crypto/configure.in      | 10 ++++++++++
 2 files changed, 24 insertions(+), 2 deletions(-)

--- a/lib/crypto/c_src/Makefile.in
+++ b/lib/crypto/c_src/Makefile.in
@@ -119,11 +119,17 @@ CRYPTO_STATIC_OBJS = $(patsubst $(OBJDIR
 
 NIF_ARCHIVE = $(LIBDIR)/crypto$(TYPEMARKER).a
 
-TEST_ENGINE_OBJS = $(OBJDIR)/otp_test_engine$(TYPEMARKER).o
-
 NIF_LIB = $(LIBDIR)/crypto$(TYPEMARKER).@DED_EXT@
 CALLBACK_LIB = $(LIBDIR)/crypto_callback$(TYPEMARKER).@DED_EXT@
+
+DISABLE_OTP_TEST_ENGINE = @DISABLE_OTP_TEST_ENGINE@
+ifeq ($(DISABLE_OTP_TEST_ENGINE),yes)
+TEST_ENGINE_LIB =
+TEST_ENGINE_OBJS =
+else
 TEST_ENGINE_LIB = $(LIBDIR)/otp_test_engine$(TYPEMARKER).@DED_EXT@
+TEST_ENGINE_OBJS = $(OBJDIR)/otp_test_engine$(TYPEMARKER).o
+endif
 
 DYNAMIC_CRYPTO_LIB=@SSL_DYNAMIC_ONLY@
 # Wierd that we compile as if dynamically linking when statically
@@ -182,6 +188,8 @@ debug opt valgrind asan: $(NIF_LIB) $(CA
 
 static_lib: $(NIF_ARCHIVE)
 
+ifeq ($(DISABLE_OTP_TEST_ENGINE),yes)
+else
 $(OBJDIR)/otp_test_engine$(TYPEMARKER).o: otp_test_engine.c
 	$(V_at)$(INSTALL_DIR) $(OBJDIR)
 	$(V_CC) -c -o $@ $(filter-out -Wmissing-prototypes,$(ALL_CFLAGS)) $<
@@ -193,6 +201,7 @@ $(LIBDIR)/otp_test_engine$(TYPEMARKER).s
 $(LIBDIR)/otp_test_engine$(TYPEMARKER).dll: $(TEST_ENGINE_OBJS)
 	$(V_at)$(INSTALL_DIR) $(LIBDIR)
 	$(V_LD) $(LDFLAGS) -o $@  $(SSL_DED_LD_RUNTIME_LIBRARY_PATH) -L$(SSL_LIBDIR) $(TEST_ENGINE_OBJS) -l$(SSL_CRYPTO_LIBNAME) -l$(SSL_SSL_LIBNAME) $(SSL_EXTRA_LIBS)
+endif
 
 $(OBJDIR)/%$(TYPEMARKER).o: %.c
 	$(V_at)$(INSTALL_DIR) $(OBJDIR)
@@ -255,7 +264,10 @@ release_spec: opt
 ifeq ($(DYNAMIC_OR_WIN_CRYPTO_LIB),yes)
 	$(INSTALL_PROGRAM) $(CALLBACK_LIB) "$(RELSYSDIR)/priv/lib"
 endif
+ifeq ($(DISABLE_OTP_TEST_ENGINE),yes)
+else
 	$(INSTALL_PROGRAM) $(TEST_ENGINE_LIB) "$(RELSYSDIR)/priv/lib"
+endif
 
 release_docs_spec:
 
--- a/lib/crypto/configure.in
+++ b/lib/crypto/configure.in
@@ -156,6 +156,15 @@ AS_HELP_STRING([--disable-evp-hmac],
     *)    DISABLE_EVP_HMAC=0;;
   esac ], DISABLE_EVP_HMAC=0)
 
+
+AC_ARG_ENABLE(otp-test-engine,
+AS_HELP_STRING([--disable-otp-test-engine],
+	       [intentionally undocumented workaround]),
+[ case "$enableval" in
+    no)   DISABLE_OTP_TEST_ENGINE=yes;;
+    *)    DISABLE_OTP_TEST_ENGINE=no;;
+  esac ], DISABLE_OTP_TEST_ENGINE=no)
+
 AC_ARG_ENABLE(deprecated_warnings,
 AS_HELP_STRING([--disable-deprecated-warnings],
 	       [disable warnings for deprecated functions in cryptolib (default is to warn, except for OpenSSL 3.x where the default is not to warn)]),
@@ -826,6 +835,7 @@ AC_SUBST(SSL_DED_LD_RUNTIME_LIBRARY_PATH
 AC_SUBST(SSL_DYNAMIC_ONLY)
 AC_SUBST(DISABLE_EVP_DH)
 AC_SUBST(DISABLE_EVP_HMAC)
+AC_SUBST(DISABLE_OTP_TEST_ENGINE)
 
 AC_OUTPUT(c_src/$host/Makefile:c_src/Makefile.in)
 
