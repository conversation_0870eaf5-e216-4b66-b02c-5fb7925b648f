From dda6b050cd74a352670787a294596a9c56c21327 Mon Sep 17 00:00:00 2001
From: <PERSON>son<PERSON> <<EMAIL>>
Date: Fri, 4 May 2018 18:20:53 +0800
Subject: [PATCH] gotools: fix compilation when making cross compiler

libgo is "the runtime support library for the Go programming language.
This library is intended for use with the Go frontend."

gccgo will link target files with libgo.so which depends on libgcc_s.so.1, but
the linker will complain that it cannot find it.  That's because shared libgcc
is not present in the install directory yet.  libgo.so was made without problem
because gcc will emit -lgcc_s when compiled with -shared option.  When gotools
were being made, it was supplied with -static-libgcc thus no link option was
provided.  Check LIBGO in gcc/go/gcc-spec.c for how gccgo make a builtin spec
for linking with libgo.so

- GccgoCrossCompilation, https://github.com/golang/go/wiki/GccgoCrossCompilation
- Cross-building instructions, http://www.eglibc.org/archives/patches/msg00078.html

When 3-pass GCC compilation is used, shared libgcc runtime libraries will be
available after gcc pass2 completed and will meet the gotools link requirement
at gcc pass3
---
 gotools/Makefile.am | 4 +++-
 gotools/Makefile.in | 4 +++-
 2 files changed, 6 insertions(+), 2 deletions(-)

--- a/gotools/Makefile.am
+++ b/gotools/Makefile.am
@@ -26,6 +26,7 @@ PWD_COMMAND = $${PWDCMD-pwd}
 STAMP = echo timestamp >
 
 libgodir = ../$(target_noncanonical)/libgo
+libgccdir = ../$(target_noncanonical)/libgcc
 LIBGODEP = $(libgodir)/libgo.la
 
 if NATIVE
@@ -38,7 +39,8 @@ endif
 GOCFLAGS = $(CFLAGS_FOR_TARGET)
 GOCOMPILE = $(GOCOMPILER) $(GOCFLAGS)
 
-AM_LDFLAGS = -L $(libgodir) -L $(libgodir)/.libs
+AM_LDFLAGS = -L $(libgodir) -L $(libgodir)/.libs \
+	-L $(libgccdir) -L $(libgccdir)/.libs -lgcc_s
 GOLINK = $(GOCOMPILER) $(GOCFLAGS) $(AM_GOCFLAGS) $(LDFLAGS) $(AM_LDFLAGS) -o $@
 
 cmdsrcdir = $(srcdir)/../libgo/go/cmd
--- a/gotools/Makefile.in
+++ b/gotools/Makefile.in
@@ -252,13 +252,15 @@ mkinstalldirs = $(SHELL) $(toplevel_srcd
 PWD_COMMAND = $${PWDCMD-pwd}
 STAMP = echo timestamp >
 libgodir = ../$(target_noncanonical)/libgo
+libgccdir = ../$(target_noncanonical)/libgcc
 LIBGODEP = $(libgodir)/libgo.la
 @NATIVE_FALSE@GOCOMPILER = $(GOC)
 
 # Use the compiler we just built.
 @NATIVE_TRUE@GOCOMPILER = $(GOC_FOR_TARGET) $(XGCC_FLAGS_FOR_TARGET)
 GOCOMPILE = $(GOCOMPILER) $(GOCFLAGS)
-AM_LDFLAGS = -L $(libgodir) -L $(libgodir)/.libs
+AM_LDFLAGS = -L $(libgodir) -L $(libgodir)/.libs \
+	-L $(libgccdir) -L $(libgccdir)/.libs -lgcc_s
 GOLINK = $(GOCOMPILER) $(GOCFLAGS) $(AM_GOCFLAGS) $(LDFLAGS) $(AM_LDFLAGS) -o $@
 cmdsrcdir = $(srcdir)/../libgo/go/cmd
 go_cmd_go_files = \
