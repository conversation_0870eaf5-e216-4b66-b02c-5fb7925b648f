commit d8c570a1531035c3e26bcd94741e5f5b9c36b5d9
Author: <PERSON> <<EMAIL>>
Date:   Mon Mar 5 00:51:01 2012 +0000

    gcc: do not emit references to _savegpr_* and _restgpr_* on powerpc, as they are tricky to deal with wrt. libgcc. they cannot be linked dynamically
    
    SVN-Revision: 30814
--- a/gcc/config/rs6000/rs6000.c
+++ b/gcc/config/rs6000/rs6000.c
@@ -26981,7 +26981,7 @@ rs6000_savres_strategy (rs6000_stack_t *
   /* Define cutoff for using out-of-line functions to save registers.  */
   if (DEFAULT_ABI == ABI_V4 || TARGET_ELF)
     {
-      if (!optimize_size)
+      if (1)
 	{
 	  strategy |= SAVE_INLINE_FPRS | REST_INLINE_FPRS;
 	  strategy |= SAVE_INLINE_GPRS | REST_INLINE_GPRS;
