Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Sat Apr 21 03:02:39 2012 +0000

    gcc: add patch to make the getenv() spec function nonfatal if requested environment variable is unset
    
    SVN-Revision: 31390

--- a/gcc/gcc.c
+++ b/gcc/gcc.c
@@ -9281,8 +9281,10 @@ getenv_spec_function (int argc, const ch
     value = varname;
 
   if (!value)
-    fatal_error (input_location,
-		 "environment variable %qs not defined", varname);
+    {
+      warning (input_location, "environment variable %qs not defined", varname);
+      value = "";
+    }
 
   /* We have to escape every character of the environment variable so
      they are not interpreted as active spec characters.  A
