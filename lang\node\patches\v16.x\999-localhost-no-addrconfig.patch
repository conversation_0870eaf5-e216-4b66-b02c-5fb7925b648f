Description: do not use dns.ADDRCONFIG for localhost
 it fails on IPv6-only systems. Setting it with libc fails on linux.
 https://github.com/nodejs/node/issues/33279
Author: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Last-Update: 2020-06-11
Bug-Debian: https://bugs.debian.org/962318
Forwarded: https://github.com/nodejs/node/issues/33816
--- a/lib/net.js
+++ b/lib/net.js
@@ -1,4 +1,5 @@
 // Copyright Joyent, Inc. and other Node contributors.
+
 //
 // Permission is hereby granted, free of charge, to any person obtaining a
 // copy of this software and associated documentation files (the
@@ -1149,13 +1150,6 @@ function lookupAndConnect(self, options)
     hints: options.hints || 0
   };
 
-  if (!isWindows &&
-      dnsopts.family !== 4 &&
-      dnsopts.family !== 6 &&
-      dnsopts.hints === 0) {
-    dnsopts.hints = dns.ADDRCONFIG;
-  }
-
   debug('connect: find host', host);
   debug('connect: dns options', dnsopts);
   self._host = host;
