From e048e9c65a52f0879d482531e70735c1d314d43a Mon Sep 17 00:00:00 2001
From: Chrostoper Ertl <<EMAIL>>
Date: Thu, 28 Nov 2019 17:06:39 +0000
Subject: [PATCH 10/11] lanp: Fix buffer overflows in get_lan_param_select
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

Partial fix for CVE-2020-5208, see
https://github.com/ipmitool/ipmitool/security/advisories/GHSA-g659-9qxw-p7cp

The `get_lan_param_select` function is missing a validation check on the
response’s `data_len`, which it then returns to caller functions, where
stack buffer overflow can occur.
---
 lib/ipmi_lanp.c | 14 +++++++-------
 1 file changed, 7 insertions(+), 7 deletions(-)

--- a/lib/ipmi_lanp.c
+++ b/lib/ipmi_lanp.c
@@ -1809,7 +1809,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 		/* set new ipaddr */
 		memcpy(data+3, temp, 4);
 		printf("Setting LAN Alert %d IP Address to %d.%d.%d.%d\n", alert,
@@ -1824,7 +1824,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 		/* set new macaddr */
 		memcpy(data+7, temp, 6);
 		printf("Setting LAN Alert %d MAC Address to "
@@ -1838,7 +1838,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 
 		if (strncasecmp(argv[1], "def", 3) == 0 ||
 		    strncasecmp(argv[1], "default", 7) == 0) {
@@ -1864,7 +1864,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 
 		if (strncasecmp(argv[1], "on", 2) == 0 ||
 		    strncasecmp(argv[1], "yes", 3) == 0) {
@@ -1889,7 +1889,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 
 		if (strncasecmp(argv[1], "pet", 3) == 0) {
 			printf("Setting LAN Alert %d destination to PET Trap\n", alert);
@@ -1917,7 +1917,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 
 		if (str2uchar(argv[1], &data[2]) != 0) {
 			lprintf(LOG_ERR, "Invalid time: %s", argv[1]);
@@ -1933,7 +1933,7 @@ ipmi_lan_alert_set(struct ipmi_intf * in
 		if (p == NULL) {
 			return (-1);
 		}
-		memcpy(data, p->data, p->data_len);
+		memcpy(data, p->data, __min(p->data_len, sizeof(data)));
 
 		if (str2uchar(argv[1], &data[3]) != 0) {
 			lprintf(LOG_ERR, "Invalid retry: %s", argv[1]);
