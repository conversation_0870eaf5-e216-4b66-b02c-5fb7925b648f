--- a/tools/icu/icu-generic.gyp
+++ b/tools/icu/icu-generic.gyp
@@ -106,6 +106,7 @@
           'sources': [
             '<@(icu_src_i18n)'
           ],
+          'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
           'include_dirs': [
             '<(icu_path)/source/i18n',
           ],
@@ -114,6 +115,7 @@
           ],
           'dependencies': [ 'icuucx', 'icu_implementation', 'icu_uconfig', 'icu_uconfig_target' ],
           'direct_dependent_settings': {
+            'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
             'include_dirs': [
               '<(icu_path)/source/i18n',
             ],
@@ -200,6 +202,7 @@
               # full data - no trim needed
               'sources': [ '<(SHARED_INTERMEDIATE_DIR)/icudt<(icu_ver_major)_dat.<(icu_asm_ext)' ],
               'dependencies': [ 'genccode#host', 'icupkg#host', 'icu_implementation#host', 'icu_uconfig' ],
+              'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
               'include_dirs': [
                 '<(icu_path)/source/common',
               ],
@@ -284,6 +287,7 @@
               # This file contains the small ICU data
               'sources': [ '<(SHARED_INTERMEDIATE_DIR)/icusmdt<(icu_ver_major)_dat.<(icu_asm_ext)' ],
               # for umachine.h
+              'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
               'include_dirs': [
                 '<(icu_path)/source/common',
               ],
@@ -300,6 +304,7 @@
       'sources': [
         '<@(icu_src_stubdata)'
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
       ],
@@ -339,6 +344,7 @@
           '_XOPEN_SOURCE_EXTENDED=0',
         ]}],
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
       ],
@@ -348,6 +354,7 @@
       'cflags_c': ['-std=c99'],
       'export_dependent_settings': [ 'icu_uconfig', 'icu_uconfig_target' ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(icu_path)/source/common',
         ],
@@ -378,6 +385,7 @@
         '<(icu_path)/source/tools/toolutil/dbgutil.cpp',
         '<(icu_path)/source/tools/toolutil/dbgutil.h',
       ],
+      'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
       'include_dirs': [
         '<(icu_path)/source/common',
         '<(icu_path)/source/i18n',
@@ -397,6 +405,7 @@
         }]
       ],
       'direct_dependent_settings': {
+        'include_dirs!': [ '<!@(echo "$STAGING_DIR"/usr/include)' ],
         'include_dirs': [
           '<(icu_path)/source/common',
           '<(icu_path)/source/i18n',
@@ -418,6 +427,7 @@
       'target_name': 'genrb',
       'type': 'executable',
       'toolsets': [ 'host' ],
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools', 'icu_implementation' ],
       'sources': [
         '<@(icu_src_genrb)'
@@ -440,6 +450,7 @@
       'target_name': 'iculslocs',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         'iculslocs.cc',
@@ -458,6 +469,7 @@
       'target_name': 'icupkg',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_icupkg)',
@@ -475,6 +487,7 @@
       'target_name': 'genccode',
       'toolsets': [ 'host' ],
       'type': 'executable',
+      'libraries!':[ '-lcrypto', '-lssl', '-lz', '-lhttp_parser', '-luv', '-lnghttp2', '-lcares' ],
       'dependencies': [ 'icutools' ],
       'sources': [
         '<@(icu_src_genccode)',
