commit 565988ab47bd9b96b50608564aee2104aeb4b7ae
Author: <PERSON> <<EMAIL>>
Date:   Tue Dec 13 14:20:49 2016 +0100

    gcc: rip out transactional memory related bloat from crtbegin
    
    Slightly improves compression for each executable, saving about 4k from
    the default ar71xx rootfs
    
    Signed-off-by: <PERSON> <<EMAIL>>

--- a/libgcc/crtstuff.c
+++ b/libgcc/crtstuff.c
@@ -152,7 +152,7 @@ call_ ## FUNC (void)					\
 #endif
 
 #if !defined(USE_TM_CLONE_REGISTRY) && defined(OBJECT_FORMAT_ELF)
-# define USE_TM_CLONE_REGISTRY 1
+# define USE_TM_CLONE_REGISTRY 0
 #endif
 
 /* We do not want to add the weak attribute to the declarations of these
