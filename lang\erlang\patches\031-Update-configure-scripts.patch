From 4d48c33d413957dc95fc8b921f9708eb0705c796 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 16 Feb 2022 09:28:38 +0100
Subject: [PATCH] Update configure scripts

---
 lib/crypto/configure | 17 +++++++++++++++++
 1 file changed, 17 insertions(+)

--- a/lib/crypto/configure
+++ b/lib/crypto/configure
@@ -621,6 +621,7 @@ ac_includes_default="\
 
 ac_subst_vars='LTLIBOBJS
 LIBOBJS
+DISABLE_OTP_TEST_ENGINE
 DISABLE_EVP_HMAC
 DISABLE_EVP_DH
 SSL_DYNAMIC_ONLY
@@ -747,6 +748,7 @@ with_ssl_rpath
 enable_dynamic_ssl_lib
 enable_evp_dh
 enable_evp_hmac
+enable_otp_test_engine
 enable_deprecated_warnings
 enable_fips
 '
@@ -1420,6 +1422,8 @@ Optional Features:
                           the crypto NIF
   --disable-evp-dh        intentionally undocumented workaround
   --disable-evp-hmac      intentionally undocumented workaround
+  --disable-otp-test-engine
+                          intentionally undocumented workaround
   --disable-deprecated-warnings
                           disable warnings for deprecated functions in
                           cryptolib (default is to warn, except for OpenSSL
@@ -5096,6 +5100,18 @@ else
 fi
 
 
+
+# Check whether --enable-otp-test-engine was given.
+if test "${enable_otp_test_engine+set}" = set; then :
+  enableval=$enable_otp_test_engine;  case "$enableval" in
+    no)   DISABLE_OTP_TEST_ENGINE=yes;;
+    *)    DISABLE_OTP_TEST_ENGINE=no;;
+  esac
+else
+  DISABLE_OTP_TEST_ENGINE=no
+fi
+
+
 # Check whether --enable-deprecated_warnings was given.
 if test "${enable_deprecated_warnings+set}" = set; then :
   enableval=$enable_deprecated_warnings;  case "$enableval" in
@@ -6319,6 +6335,7 @@ fi
 
 
 
+
 
 
 
