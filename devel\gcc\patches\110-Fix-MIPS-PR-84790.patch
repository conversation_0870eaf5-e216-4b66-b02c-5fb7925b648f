Fix https://gcc.gnu.org/bugzilla/show_bug.cgi?id=84790.
MIPS16 functions have a static assembler prologue which clobbers
registers v0 and v1. Add these register clobbers to function call
instructions.

--- a/gcc/config/mips/mips.c
+++ b/gcc/config/mips/mips.c
@@ -3098,6 +3098,12 @@ mips_emit_call_insn (rtx pattern, rtx or
       emit_insn (gen_update_got_version ());
     }
 
+  if (TARGET_MIPS16 && TARGET_USE_GOT)
+    {
+      clobber_reg (&CALL_INSN_FUNCTION_USAGE (insn), MIPS16_PIC_TEMP);
+      clobber_reg (&CALL_INSN_FUNCTION_USAGE (insn), MIPS_PROLOGUE_TEMP (word_mode));
+    }
+
   if (TARGET_MIPS16
       && TARGET_EXPLICIT_RELOCS
       && TARGET_CALL_CLOBBERED_GP)
