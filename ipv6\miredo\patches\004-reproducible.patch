From: <PERSON><PERSON> <<EMAIL>>
Date: Fri, 6 Feb 2015 11:33:20 +0100
Subject: Fix reproducibility issues

We replace unreproducible CC macros with
N/A placeholders. This fixes #776716.
---
 src/main.c | 6 +++---
 1 file changed, 3 insertions(+), 3 deletions(-)

--- a/src/main.c
+++ b/src/main.c
@@ -92,10 +92,11 @@ miredo_version (void)
 #ifndef VERSION
 # define VERSION "unknown version"
 #endif
+	const char* UNKNOWN = "N/A";
 	printf (_("Miredo: Teredo IPv6 tunneling software %s (%s)\n"
 	          " built %s on %s (%s)\n"),
-	        VERSION, PACKAGE_HOST, __DATE__,
-	        PACKAGE_BUILD_HOSTNAME, PACKAGE_BUILD);
+	        VERSION, UNKNOWN, UNKNOWN,
+	        UNKNOWN, UNKNOWN);
 	printf (_("Configured with: %s\n"), PACKAGE_CONFIGURE_INVOCATION);
 	puts (_("Written by <PERSON><PERSON>.\n"));
 
