--- a/include/gettext.h
+++ b/include/gettext.h
@@ -182,7 +182,7 @@ npgettext_aux (const char *domain,
   (((__GNUC__ >= 3 || __GNUG__ >= 2) && !defined(__STRICT_ANSI__)) \
    /* || __STDC_VERSION__ >= 199901L */ )
 
-#if !_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS
+#if !defined(_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS)
 #include <stdlib.h>
 #endif
 
@@ -206,7 +206,7 @@ dcpgettext_expr (const char *domain,
   size_t msgctxt_len = strlen (msgctxt) + 1;
   size_t msgid_len = strlen (msgid) + 1;
   const char *translation;
-#if _LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS
+#if defined(_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS)
   char msg_ctxt_id[msgctxt_len + msgid_len];
 #else
   char buf[1024];
@@ -221,7 +221,7 @@ dcpgettext_expr (const char *domain,
       msg_ctxt_id[msgctxt_len - 1] = '\004';
       memcpy (msg_ctxt_id + msgctxt_len, msgid, msgid_len);
       translation = dcgettext (domain, msg_ctxt_id, category);
-#if !_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS
+#if !defined(_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS)
       if (msg_ctxt_id != buf)
         free (msg_ctxt_id);
 #endif
@@ -252,7 +252,7 @@ dcnpgettext_expr (const char *domain,
   size_t msgctxt_len = strlen (msgctxt) + 1;
   size_t msgid_len = strlen (msgid) + 1;
   const char *translation;
-#if _LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS
+#if defined(_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS)
   char msg_ctxt_id[msgctxt_len + msgid_len];
 #else
   char buf[1024];
@@ -267,7 +267,7 @@ dcnpgettext_expr (const char *domain,
       msg_ctxt_id[msgctxt_len - 1] = '\004';
       memcpy (msg_ctxt_id + msgctxt_len, msgid, msgid_len);
       translation = dcngettext (domain, msg_ctxt_id, msgid_plural, n, category);
-#if !_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS
+#if !defined(_LIBGETTEXT_HAVE_VARIABLE_SIZE_ARRAYS)
       if (msg_ctxt_id != buf)
         free (msg_ctxt_id);
 #endif
