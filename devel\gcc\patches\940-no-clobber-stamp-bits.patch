commit 548d9a008ff265e9eaa3c7e0e6e301c6bd5645e6
Author: <PERSON> <<EMAIL>>
Date:   Fri Dec 12 17:01:57 2014 +0000

    gcc: don't clobber stamp-bits with a symlink to itself
    
    Several versions of gcc have an issue in libstdc++v3 where the build may
    clobber stamp-bits with a link to itself.  This doesn't manifest itself
    on all systems.  On several Ubuntu systems, this doesn't appear to be a
    problem, but it is an issue on Fedora 16 systems.
    
    To fix the issue, we'll simply filter out stamp-bits from the symlinks
    to be generated.
    
    Note: gcc 4.4.7 is unaffected by this issue, so no fix is necessary
    there.
    
    Signed-off-by: <PERSON> <<EMAIL>>
    
    SVN-Revision: 43669


--- a/libstdc++-v3/include/Makefile.in
+++ b/libstdc++-v3/include/Makefile.in
@@ -1474,7 +1474,7 @@ stamp-bits: ${bits_headers}
 	@$(STAMP) stamp-bits
 
 stamp-bits-sup: stamp-bits ${bits_sup_headers}
-	@-cd ${bits_builddir} && $(LN_S) $? . 2>/dev/null
+	@-cd ${bits_builddir} && $(LN_S) $(filter-out stamp-bits,$?) . 2>/dev/null
 	@$(STAMP) stamp-bits-sup
 
 stamp-c_base: ${c_base_headers}
