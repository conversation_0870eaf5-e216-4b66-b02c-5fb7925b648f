--- a/lib/internal/modules/cjs/loader.js
+++ b/lib/internal/modules/cjs/loader.js
@@ -1326,7 +1326,8 @@ Module._initPaths = function() {
     path.resolve(process.execPath, '..') :
     path.resolve(process.execPath, '..', '..');
 
-  const paths = [path.resolve(prefixDir, 'lib', 'node')];
+  const paths = [path.resolve(prefixDir, 'lib', 'node'),
+                 path.resolve(prefixDir, 'lib', 'node_modules')];
 
   if (homeDir) {
     ArrayPrototypeUnshift(paths, path.resolve(homeDir, '.node_libraries'));
