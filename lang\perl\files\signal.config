# Signal table helper

(defined($owrt:sigs)) {
	sig_name="$owrt:sigs"
	(($owrt:libc eq 'glibc') || ($owrt:libc eq 'uclibc')) {
		sig_name="$sig_name NUM32 NUM33 RTMIN NUM35 NUM36"
	}
	($owrt:libc eq 'musl') {
		sig_name="$sig_name NUM32 NUM33 NUM34 RTMIN NUM36"
	}
	
	sig_name="$sig_name @{[map({qq/NUM\$_/} 37..$owrt:sig_count - 2)]} RTMAX $owrt:sig_name_extra"
	sig_num="@{[0..$owrt:sig_count - 1]} $owrt:sig_num_extra "
	sig_count="$owrt:sig_count"
	sig_size="@{[scalar(split(q/ /, $sig_name))]}"
	sig_name_init="@{[join(q/, /, map({qq/\qq\$_\qq/} split(q/ /, $sig_name)))]}, 0"
	sig_num_init="@{[join(q/, /, split(q/ /, $sig_num))]}, 0"
}
