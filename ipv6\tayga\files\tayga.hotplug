#!/bin/sh

if [ "$ACTION" = ifup ]; then
	. /lib/functions.sh

	include /lib/network
	scan_interfaces

	update_tunnel() {
		local cfg="$1"

		local proto
		config_get proto "$cfg" proto
		[ "$proto" = tayga ] || return 0

		local wandev4
		config_get wandev4 "$cfg" wan4_device "$(find_tayga_wanif4)"

		local wandev6
		config_get wandev6 "$cfg" wan6_device "$(find_tayga_wanif6)"

		[ "$wandev4" = "$DEVICE" ] || [ "$wandev6" = "$DEVICE" ] || return 0

		local wanip4=$(find_tayga_wanip4 "$wandev4")
		local wanip6=$(find_tayga_wanip6 "$wandev6")

		[ -n "$wanip4" ] && [ -n "$wanip6" ] && {
			uci_set_state network "$cfg" ipv4addr "$wanip4"
			uci_set_state network "$cfg" ipv6addr "$wanip6"

			logger -t tayga-update "Re-establishing tayga NAT64 due to change on $INTERFACE ($DEVICE)"
			ifup "$cfg" &
		}
	}

	config_foreach update_tunnel interface
fi
