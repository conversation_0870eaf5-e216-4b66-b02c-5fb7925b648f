commit ecf7671b769fe96f7b5134be442089f8bdba55d2
Author: <PERSON> <<EMAIL>>
Date:   Thu Aug 4 20:29:45 2016 +0200

gcc: add a patch to generate better code with Os on mips

Also happens to reduce compressed code size a bit

Signed-off-by: <PERSON> <<EMAIL>>

--- a/gcc/config/mips/mips.c
+++ b/gcc/config/mips/mips.c
@@ -19790,7 +19790,7 @@ mips_option_override (void)
     flag_pcc_struct_return = 0;
 
   /* Decide which rtx_costs structure to use.  */
-  if (optimize_size)
+  if (0 && optimize_size)
     mips_cost = &mips_rtx_cost_optimize_size;
   else
     mips_cost = &mips_rtx_cost_data[mips_tune];
