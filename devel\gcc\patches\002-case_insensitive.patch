commit 81cc26c706b2bc8c8c1eb1a322e5c5157900836e
Author: <PERSON> <<EMAIL>>
Date:   Sun Oct 19 21:45:51 2014 +0000

    gcc: do not assume that the Mac OS X filesystem is case insensitive
    
    Signed-off-by: <PERSON> <<EMAIL>>
    
    SVN-Revision: 42973

--- a/include/filenames.h
+++ b/include/filenames.h
@@ -43,11 +43,6 @@ extern "C" {
 #  define IS_DIR_SEPARATOR(c) IS_DOS_DIR_SEPARATOR (c)
 #  define IS_ABSOLUTE_PATH(f) IS_DOS_ABSOLUTE_PATH (f)
 #else /* not DOSish */
-#  if defined(__APPLE__)
-#    ifndef HAVE_CASE_INSENSITIVE_FILE_SYSTEM
-#      define HAVE_CASE_INSENSITIVE_FILE_SYSTEM 1
-#    endif
-#  endif /* __APPLE__ */
 #  define HAS_DRIVE_SPEC(f) (0)
 #  define IS_DIR_SEPARATOR(c) IS_UNIX_DIR_SEPARATOR (c)
 #  define IS_ABSOLUTE_PATH(f) IS_UNIX_ABSOLUTE_PATH (f)
