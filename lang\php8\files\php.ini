[PHP]

;;;;;;;;;;;;;;;;;;;;
; Language Options ;
;;;;;;;;;;;;;;;;;;;;

engine = On
;short_open_tag = Off
precision = 12
output_buffering = Off
;output_handler =
zlib.output_compression = Off
;zlib.output_compression_level = -1
;zlib.output_handler =
implicit_flush = Off
unserialize_callback_func =
;unserialize_max_depth = 4096
serialize_precision = 100
;open_basedir =
disable_functions =
disable_classes =
;highlight.string = #DD0000
;highlight.comment = #FF9900
;highlight.keyword = #007700
;highlight.default = #0000BB
;highlight.html = #000000
;ignore_user_abort = On
;realpath_cache_size = 16k
;realpath_cache_ttl = 120
;zend.enable_gc = On
;zend.multibyte = Off
;zend.script_encoding =
;zend.exception_ignore_args = On
;zend.exception_string_param_max_len = 0

;;;;;;;;;;;;;;;;;
; Miscellaneous ;
;;;;;;;;;;;;;;;;;

expose_php = On

;;;;;;;;;;;;;;;;;;;
; Resource Limits ;
;;;;;;;;;;;;;;;;;;;

max_execution_time = 30
max_input_time = 60
;max_input_nesting_level = 64
;max_input_vars = 1000
memory_limit = 8M

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; Error handling and logging ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

error_reporting = E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT
display_errors = On
display_startup_errors = Off
log_errors = Off
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
;report_zend_debug = 0
track_errors = Off
;html_errors = Off
;docref_root = "/phpmanual/"
;docref_ext = .html
;error_prepend_string = "<span style='color: #ff0000'>"
;error_append_string = "</span>"
;error_log = syslog
;syslog.ident = php
;syslog.facility = user
;syslog.filter = ascii

;;;;;;;;;;;;;;;;;
; Data Handling ;
;;;;;;;;;;;;;;;;;

;arg_separator.output = "&amp;"
;arg_separator.input = ";&"
variables_order = "EGPCS"
request_order = "GP"
register_argc_argv = On
auto_globals_jit = On
;enable_post_data_reading = Off
post_max_size = 8M
auto_prepend_file =
auto_append_file =
default_mimetype = "text/html"
;default_charset = "UTF-8"
;internal_encoding =
;input_encoding =
;output_encoding =

;;;;;;;;;;;;;;;;;;;;;;;;;
; Paths and Directories ;
;;;;;;;;;;;;;;;;;;;;;;;;;

;include_path = ".:/php/includes"
doc_root = "/www"
user_dir =
extension_dir = "/usr/lib/php8"
;sys_temp_dir = "/tmp"
enable_dl = On
;cgi.force_redirect = 1
;cgi.nph = 1
;cgi.redirect_status_env =
cgi.fix_pathinfo = 1
;cgi.discard_path = 1
;fastcgi.impersonate = 1
;fastcgi.logging = 0
;cgi.rfc2616_headers = 0
;cgi.check_shebang_line = 1

;;;;;;;;;;;;;;;;
; File Uploads ;
;;;;;;;;;;;;;;;;

file_uploads = On
upload_tmp_dir = "/tmp"
upload_max_filesize = 2M
max_file_uploads = 20

;;;;;;;;;;;;;;;;;;
; Fopen wrappers ;
;;;;;;;;;;;;;;;;;;

allow_url_fopen = On
allow_url_include = Off
;from = "<EMAIL>"
;user_agent = "PHP"
default_socket_timeout = 60
;auto_detect_line_endings = Off
