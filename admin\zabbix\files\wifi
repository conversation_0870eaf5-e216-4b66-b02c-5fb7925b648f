#see https://openwrt.org/docs/guide-user/services/network_monitoring/zabbix for ready to use templates

# wifi interface discovery
# example: {"data":[{"{#IF}":"wlan0", "{#MODE}":"ap", "{#SSID}":"Openwrt", "{#NET}":"lan", "{#DEV}":"radio0", "{#ENC}":"psk2+ccmp", "{#TYPE}":"mac80211", "{#HWMODE}":"11ng", "{#CHANNEL}":"11", "{#BSSID}":"xx:xx:xx:xx:xx:xx"}]}
UserParameter=wifi.ifdiscovery, lua -l ubus -l iwinfo -e 'u=ubus.connect();list="{\"data\":[";stat=u:call("network.wireless", "status", {});for dev, dev_table in pairs(stat) do for i, iface in pairs(dev_table["interfaces"]) do c=iface["config"];i=iface["ifname"];t=iwinfo.type(i);iw=iwinfo[t];e = iw.encryption(i);e = e and e.description or "None";n = table.concat(c["network"]," ");list=list.."{\"{#IF}\":\""..i.."\", \"{#MODE}\":\""..iw.mode(i).."\", \"{#SSID}\":\""..c["ssid"].."\", \"{#NET}\":\""..n.."\", \"{#DEV}\":\""..dev.."\", \"{#ENC}\":\""..e.."\", \"{#TYPE}\":\""..t.."\", \"{#HWMODE}\":\"".."?".."\", \"{#CHANNEL}\":\""..iw.channel(i).."\", \"{#BSSID}\":\""..iw.bssid(i).."\"},";end;end;list=string.gsub(list,",$","");print(list.."]}")'


#iwinfo info (you need {#IF} as parameter, like 'wlan0')
UserParameter=wifi.iwinfo.channel[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].channel('$1'))"
UserParameter=wifi.iwinfo.frequency[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].frequency('$1'))"
UserParameter=wifi.iwinfo.txpower[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].txpower('$1'))"
UserParameter=wifi.iwinfo.bitrate[*],lua -l iwinfo -e "b = iwinfo[iwinfo.type('$1')].bitrate('$1'); print(b or '0')"
UserParameter=wifi.iwinfo.signal[*],lua -l iwinfo -e "s = iwinfo[iwinfo.type('$1')].signal('$1'); print(s or '-255')"
UserParameter=wifi.iwinfo.noise[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].noise('$1'))"
UserParameter=wifi.iwinfo.quality[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].quality('$1'))"
UserParameter=wifi.iwinfo.quality_max[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].quality_max('$1'))"
UserParameter=wifi.iwinfo.mode[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].mode('$1'))"
UserParameter=wifi.iwinfo.ssid[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].ssid('$1'))"
UserParameter=wifi.iwinfo.bssid[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].bssid('$1'))"
UserParameter=wifi.iwinfo.country[*],lua -l iwinfo -e "print(iwinfo[iwinfo.type('$1')].country('$1'))"
UserParameter=wifi.iwinfo.nbusers[*],lua -l iwinfo -e "n = 0; for _,_ in pairs(iwinfo[iwinfo.type('$1')].assoclist('$1')) do n = n + 1 end; print(n)"
UserParameter=wifi.iwinfo.encryption[*],lua -l iwinfo -e "e = iwinfo[iwinfo.type('$1')].encryption('$1'); print(e and e.description or 'None')"
UserParameter=wifi.iwinfo.hwmode[*],lua -l iwinfo -e "x=iwinfo[iwinfo.type('$1')].hwmodelist('$1'); print((x.a and 'a' or '')..(x.b and 'b' or '')..(x.g and 'g' or '')..(x.n and 'n' or ''))"

