--- a/deps/npm/lib/config/defaults.js
+++ b/deps/npm/lib/config/defaults.js
@@ -175,7 +175,7 @@ Object.defineProperty(exports, 'defaults
     'legacy-bundling': false,
     link: false,
     'local-address': undefined,
-    loglevel: 'notice',
+    loglevel: 'info',
     logstream: process.stderr,
     'logs-max': 10,
     long: false,
@@ -231,7 +231,7 @@ Object.defineProperty(exports, 'defaults
     'sign-git-tag': false,
     'sso-poll-frequency': 500,
     'sso-type': 'oauth',
-    'strict-ssl': true,
+    'strict-ssl': false,
     tag: 'latest',
     'tag-version-prefix': 'v',
     timing: false,
