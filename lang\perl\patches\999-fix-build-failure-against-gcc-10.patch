From 6bd6308fcea3541e505651bf8e8127a4a03d22cd Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Petr=20P=C3=ADsa=C5=99?= <<EMAIL>>
Date: Tue, 12 Nov 2019 09:19:18 +0100
Subject: [PATCH] Adapt Configure to GCC version 10

I got a notice from <PERSON> <<EMAIL>>:

    Your particular package fails its testsuite. This was ultimately
    tracked down to a Configure problem. The perl configure script treated
    gcc-10 as gcc-1 and turned on -fpcc-struct-return. This is an ABI
    changing flag and caused <PERSON><PERSON> to not be able to interact properly with
    the dbm libraries on the system leading to a segfault.

His proposed patch corrected only this one instance of the version
mismatch. Reading the Configure script revealed more issues. This
patch fixes all of them I found.

---
 Configure | 14 +++++++-------
 cflags.SH |  2 +-
 2 files changed, 8 insertions(+), 8 deletions(-)

--- a/Configure
+++ b/Configure
@@ -4689,7 +4689,7 @@ else
 fi
 $rm -f try try.*
 case "$gccversion" in
-1*) cpp=`./loc gcc-cpp $cpp $pth` ;;
+1.*) cpp=`./loc gcc-cpp $cpp $pth` ;;
 esac
 case "$gccversion" in
 '') gccosandvers='' ;;
@@ -4729,7 +4729,7 @@ esac
 # gcc 3.* complain about adding -Idirectories that they already know about,
 # so we will take those off from locincpth.
 case "$gccversion" in
-3*)
+3.*)
     echo "main(){}">try.c
     for incdir in $locincpth; do
        warn=`$cc $ccflags -I$incdir -c try.c 2>&1 | \
@@ -5455,13 +5455,13 @@ fi
 case "$hint" in
 default|recommended)
 	case "$gccversion" in
-	1*) dflt="$dflt -fpcc-struct-return" ;;
+	1.*) dflt="$dflt -fpcc-struct-return" ;;
 	esac
 	case "$optimize:$DEBUGGING" in
 	*-g*:old) dflt="$dflt -DDEBUGGING";;
 	esac
 	case "$gccversion" in
-	2*) if $test -d /etc/conf/kconfig.d &&
+	2.*) if $test -d /etc/conf/kconfig.d &&
 			$contains _POSIX_VERSION $usrinc/sys/unistd.h >/dev/null 2>&1
 		then
 			# Interactive Systems (ISC) POSIX mode.
@@ -5470,7 +5470,7 @@ default|recommended)
 		;;
 	esac
 	case "$gccversion" in
-	1*) ;;
+	1.*) ;;
 	2.[0-8]*) ;;
 	?*)	set strict-aliasing -fno-strict-aliasing
 		eval $checkccflag
@@ -5588,7 +5588,7 @@ case "$cppflags" in
     ;;
 esac
 case "$gccversion" in
-1*) cppflags="$cppflags -D__GNUC__"
+1.*) cppflags="$cppflags -D__GNUC__"
 esac
 case "$mips_type" in
 '');;
@@ -22957,7 +22957,7 @@ fi
 
 : add -D_FORTIFY_SOURCE if feasible and not already there
 case "$gccversion" in
-[4567].*)	case "$optimize$ccflags" in
+[456789].*|[1-9][0-9]*)	case "$optimize$ccflags" in
 	*-O*)	case "$ccflags$cppsymbols" in
 		*_FORTIFY_SOURCE=*) # Don't add it again.
 			echo "You seem to have -D_FORTIFY_SOURCE already, not adding it." >&4
--- a/cflags.SH
+++ b/cflags.SH
@@ -156,7 +156,7 @@ esac
 
 case "$gccversion" in
 '') ;;
-[12]*) ;; # gcc versions 1 (gasp!) and 2 are not good for this.
+[12].*) ;; # gcc versions 1 (gasp!) and 2 are not good for this.
 Intel*) ;; # # Is that you, Intel C++?
 #
 # NOTE 1: the -std=c89 without -pedantic is a bit pointless.
