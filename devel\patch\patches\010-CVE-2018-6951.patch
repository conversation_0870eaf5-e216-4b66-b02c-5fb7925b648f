From 9bf998b5fcbcde1dea0e472dc1538abb97e9012e Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 12 Feb 2018 16:48:24 +0100
Subject: [PATCH] Fix segfault with mangled rename patch

http://savannah.gnu.org/bugs/?53132
* src/pch.c (intuit_diff_type): Ensure that two filenames are specified
for renames and copies (fix the existing check).
---
 src/pch.c | 3 ++-
 1 file changed, 2 insertions(+), 1 deletion(-)

--- a/src/pch.c
+++ b/src/pch.c
@@ -974,7 +974,8 @@ intuit_diff_type (bool need_header, mode
     if ((pch_rename () || pch_copy ())
 	&& ! inname
 	&& ! ((i == OLD || i == NEW) &&
-	      p_name[! reverse] &&
+	      p_name[reverse] && p_name[! reverse] &&
+	      name_is_valid (p_name[reverse]) &&
 	      name_is_valid (p_name[! reverse])))
       {
 	say ("Cannot %s file without two valid file names\n", pch_rename () ? "rename" : "copy");
