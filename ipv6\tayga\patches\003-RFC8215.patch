--- a/addrmap.c
+++ b/addrmap.c
@@ -44,7 +44,9 @@ int validate_ip4_addr(const struct in_ad
 int validate_ip6_addr(const struct in6_addr *a)
 {
 	/* Well-known prefix for NAT64 */
-	if (a->s6_addr32[0] == WKPF && !a->s6_addr32[1] && !a->s6_addr32[2])
+        if (a->s6_addr32[0] == WKPF &&
+	   (!a->s6_addr32[1] || (a->s6_addr16[2] == htonl(0x0001)))
+	    && !a->s6_addr32[2])
 		return 0;
 
 	/* Reserved per RFC 2373 */
