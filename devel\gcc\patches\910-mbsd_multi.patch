commit 99368862e44740ff4fd33760893f04e14f9dbdf1
Author: <PERSON> <<EMAIL>>
Date:   Tue Jul 31 00:52:27 2007 +0000

    Port the mbsd_multi patch from freewrt, which adds -fhonour-copts. This will emit warnings in packages that don't use our target cflags properly
    
    SVN-Revision: 8256

	This patch brings over a feature from MirBSD:
	* -fhonour-copts
	  If this option is not given, it's warned (depending
	  on environment variables). This is to catch errors
	  of misbuilt packages which override CFLAGS themselves.

	This patch was authored by <PERSON><PERSON> <tg at mirbsd.de>
	with copyright assignment to the FSF in effect.

--- a/gcc/c-family/c-opts.c
+++ b/gcc/c-family/c-opts.c
@@ -108,6 +108,9 @@ static int class_dump_flags;
 /* Whether any standard preincluded header has been preincluded.  */
 static bool done_preinclude;
 
+/* Check if a port honours COPTS.  */
+static int honour_copts = 0;
+
 static void handle_OPT_d (const char *);
 static void set_std_cxx98 (int);
 static void set_std_cxx11 (int);
@@ -456,6 +459,12 @@ c_common_handle_option (size_t scode, co
       flag_no_builtin = !value;
       break;
 
+    case OPT_fhonour_copts:
+      if (c_language == clk_c) {
+        honour_copts++;
+      }
+      break;
+
     case OPT_fconstant_string_class_:
       constant_string_class_name = arg;
       break;
@@ -1084,6 +1093,47 @@ c_common_init (void)
       return false;
     }
 
+  if (c_language == clk_c) {
+    char *ev = getenv ("GCC_HONOUR_COPTS");
+    int evv;
+    if (ev == NULL)
+      evv = -1;
+    else if ((*ev == '0') || (*ev == '\0'))
+      evv = 0;
+    else if (*ev == '1')
+      evv = 1;
+    else if (*ev == '2')
+      evv = 2;
+    else if (*ev == 's')
+      evv = -1;
+    else {
+      warning (0, "unknown GCC_HONOUR_COPTS value, assuming 1");
+      evv = 1; /* maybe depend this on something like MIRBSD_NATIVE?  */
+    }
+    if (evv == 1) {
+      if (honour_copts == 0) {
+        error ("someone does not honour COPTS at all in lenient mode");
+        return false;
+      } else if (honour_copts != 1) {
+        warning (0, "someone does not honour COPTS correctly, passed %d times",
+         honour_copts);
+      }
+    } else if (evv == 2) {
+      if (honour_copts == 0) {
+        error ("someone does not honour COPTS at all in strict mode");
+        return false;
+      } else if (honour_copts != 1) {
+        error ("someone does not honour COPTS correctly, passed %d times",
+         honour_copts);
+        return false;
+      }
+    } else if (evv == 0) {
+      if (honour_copts != 1)
+        inform (0, "someone does not honour COPTS correctly, passed %d times",
+         honour_copts);
+    }
+  }
+
   return true;
 }
 
--- a/gcc/c-family/c.opt
+++ b/gcc/c-family/c.opt
@@ -1412,6 +1412,9 @@ C++ ObjC++ Optimization Alias(fexception
 fhonor-std
 C++ ObjC++ Ignore Warn(switch %qs is no longer supported)
 
+fhonour-copts
+C ObjC C++ ObjC++ RejectNegative
+
 fhosted
 C ObjC
 Assume normal C execution environment.
--- a/gcc/common.opt
+++ b/gcc/common.opt
@@ -1510,6 +1510,9 @@ fguess-branch-probability
 Common Report Var(flag_guess_branch_prob) Optimization
 Enable guessing of branch probabilities.
 
+fhonour-copts
+Common RejectNegative
+
 ; Nonzero means ignore `#ident' directives.  0 means handle them.
 ; Generate position-independent code for executables if possible
 ; On SVR4 targets, it also controls whether or not to emit a
--- a/gcc/opts.c
+++ b/gcc/opts.c
@@ -1954,6 +1954,9 @@ common_handle_option (struct gcc_options
 			       opts, opts_set, loc, dc);
       break;
 
+    case OPT_fhonour_copts:
+      break;
+
     case OPT_Wlarger_than_:
       opts->x_larger_than_size = value;
       opts->x_warn_larger_than = value != -1;
--- a/gcc/doc/invoke.texi
+++ b/gcc/doc/invoke.texi
@@ -6572,6 +6572,17 @@ This option is only supported for C and
 @option{-Wall} and by @option{-Wpedantic}, which can be disabled with
 @option{-Wno-pointer-sign}.
 
+@item -fhonour-copts
+@opindex fhonour-copts
+If @env{GCC_HONOUR_COPTS} is set to 1, abort if this option is not
+given at least once, and warn if it is given more than once.
+If @env{GCC_HONOUR_COPTS} is set to 2, abort if this option is not
+given exactly once.
+If @env{GCC_HONOUR_COPTS} is set to 0 or unset, warn if this option
+is not given exactly once.
+The warning is quelled if @env{GCC_HONOUR_COPTS} is set to @samp{s}.
+This flag and environment variable only affect the C language.
+
 @item -Wstack-protector
 @opindex Wstack-protector
 @opindex Wno-stack-protector
