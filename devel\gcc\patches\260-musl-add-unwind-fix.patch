From: ktka<PERSON>v <ktkachov@138bc75d-0d04-0410-961f-82ee72b054a4>
Date: Wed, 22 Apr 2015 14:20:01 +0000 (+0000)
Subject: unwind fix for musl
X-Git-Url: https://gcc.gnu.org/git/?p=gcc.git;a=commitdiff_plain;h=a2e31d0681d8a47389b8a3552622fbd9827bcef4

unwind fix for musl

On <NAME_EMAIL>

2015-04-22  <PERSON>  <<EMAIL>>
	    Szabolcs Nagy  <<EMAIL>>

	* unwind-dw2-fde-dip.c (USE_PT_GNU_EH_FRAME): Define it on
	Linux if target provides dl_iterate_phdr.


git-svn-id: svn+ssh://gcc.gnu.org/svn/gcc/trunk@222328 138bc75d-0d04-0410-961f-82ee72b054a4
---

--- a/libgcc/unwind-dw2-fde-dip.c
+++ b/libgcc/unwind-dw2-fde-dip.c
@@ -65,6 +65,12 @@
 
 #if !defined(inhibit_libc) && defined(HAVE_LD_EH_FRAME_HDR) \
     && defined(TARGET_DL_ITERATE_PHDR) \
+    && defined(__linux__)
+# define USE_PT_GNU_EH_FRAME
+#endif
+
+#if !defined(inhibit_libc) && defined(HAVE_LD_EH_FRAME_HDR) \
+    && defined(TARGET_DL_ITERATE_PHDR) \
     && (defined(__DragonFly__) || defined(__FreeBSD__))
 # define ElfW __ElfN
 # define USE_PT_GNU_EH_FRAME
