From 682a4acb98783a7f9b5c286b308f12863599fec3 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 30 Jul 2018 21:00:52 +1000
Subject: (perl #133411) don't try to load Storable with -Dusecrosscompile

---
 dist/Storable/Makefile.PL |  9 ++++++++-
 dist/Storable/stacksize   | 10 +++++++---
 2 files changed, 15 insertions(+), 4 deletions(-)

--- a/dist/Storable/Makefile.PL
+++ b/dist/Storable/Makefile.PL
@@ -90,12 +90,19 @@ sub depend {
         # blib.pm needs arch/lib
         $extra_deps = ' Storable.pm';
     }
+    my $whichperl;
+    if ($Config::Config{usecrosscompile}) {
+        $whichperl = '$(PERLRUN)';
+    }
+    else {
+        $whichperl = '$(FULLPERLRUNINST)';
+    }
     my $linktype = uc($_[0]->{LINKTYPE});
     my $limit_pm = File::Spec->catfile('lib', 'Storable', 'Limit.pm');
     "
 $limit_pm : stacksize \$(INST_$linktype)$extra_deps
 	\$(MKPATH) \$(INST_LIB)
-	\$(FULLPERLRUNINST) stacksize $options
+	$whichperl stacksize $options
 
 release : dist
 	git tag \$(VERSION)
--- a/dist/Storable/stacksize
+++ b/dist/Storable/stacksize
@@ -7,6 +7,9 @@ use Cwd;
 use File::Spec;
 use strict;
 
+-d "lib" or mkdir "lib";
+-d "lib/Storable" or mkdir "lib/Storable";
+
 my $fn = "lib/Storable/Limit.pm";
 my $ptrsize = $Config{ptrsize};
 my ($bad1, $bad2) = (65001, 25000);
@@ -29,6 +32,10 @@ sub is_miniperl {
 }
 
 if (is_miniperl()) {
+    if ($Config{usecrosscompile}) {
+        write_limits(500, 265);
+        exit;
+    }
     die "Should not run during miniperl\n";
 }
 my $prefix = "";
@@ -68,9 +75,6 @@ if ($ENV{PERL_CORE}) {
     }
 }
 
--d "lib" or mkdir "lib";
--d "lib/Storable" or mkdir "lib/Storable";
-
 if ($^O eq "MSWin32") {
     require Win32;
     my ($str, $major, $minor) = Win32::GetOSVersion();
