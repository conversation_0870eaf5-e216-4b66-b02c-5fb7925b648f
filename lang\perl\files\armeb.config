owrt:arch=armeb
owrt:bits=32
owrt:endian=big

ccsymbols='__ARM_ARCH_3__=1'
cppccsymbols=''
cppsymbols='__ELF__=1 _FILE_OFFSET_BITS=64 __GLIBC__=2 __GLIBC_MINOR__=2 __GNUC__=3 __GNUC_MINOR__=4 __GNU_LIBRARY__=6 _LARGEFILE_SOURCE=1 _POSIX_C_SOURCE=199506L _POSIX_SOURCE=1 __STDC__=1 __USE_BSD=1 __USE_FILE_OFFSET64=1 __USE_LARGEFILE=1 __USE_MISC=1 __USE_POSIX=1 __USE_POSIX199309=1 __USE_POSIX199506=1 __USE_POSIX2=1 __USE_SVID=1 linux=1 __linux=1 __linux__=1 unix=1 __unix=1 __unix__=1'
d_casti32='define'
d_double_style_ieee='define'
d_modflproto='undef'
doublekind='4'
fpossize='20'
longdblkind='0'
need_va_copy='undef'
quadkind='3'

owrt:sig_count='64'
owrt:sigs='ZERO HUP INT QUIT ILL TRAP ABRT BUS FPE KILL USR1 SEGV USR2 PIPE ALRM TERM STKFLT CHLD CONT STOP TSTP TTIN TTOU URG XCPU XFSZ VTALRM PROF WINCH IO PWR SYS'
owrt:sig_name_extra='IOT CLD POLL UNUSED'
owrt:sig_num_extra='6 17 29 31'
