From f004b4b7197fc83e7d47ec8cbcaefffa9a922717 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Sun, 12 Mar 2017 14:00:35 +0100
Subject: [PATCH 3/4] ID:480 - ipmitool coredumps in EVP_CIPHER_CTX_init

IPMI tool coredumps due to changes introduced in ID:461. This shouldn't be
surprise as a NULL pointer is passed to init. Commit addresses this issue by
calling EVP_CIPHER_CTX_new() instead of EVP_CIPHER_CTX_init(), which is
deprecated, and by checking return value of call to former function.
---
 src/plugins/lanplus/lanplus_crypt_impl.c | 14 ++++++++++----
 1 file changed, 10 insertions(+), 4 deletions(-)

--- a/src/plugins/lanplus/lanplus_crypt_impl.c
+++ b/src/plugins/lanplus/lanplus_crypt_impl.c
@@ -165,10 +165,13 @@ lanplus_encrypt_aes_cbc_128(const uint8_
 							uint32_t        * bytes_written)
 {
 	EVP_CIPHER_CTX *ctx = NULL;
-	EVP_CIPHER_CTX_init(ctx);
+	ctx = EVP_CIPHER_CTX_new();
+	if (ctx == NULL) {
+		*bytes_written = 0;
+		return;
+	}
 	EVP_EncryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
 	EVP_CIPHER_CTX_set_padding(ctx, 0);
-	
 
 	*bytes_written = 0;
 
@@ -240,11 +243,14 @@ lanplus_decrypt_aes_cbc_128(const uint8_
 							uint32_t        * bytes_written)
 {
 	EVP_CIPHER_CTX *ctx = NULL;
-	EVP_CIPHER_CTX_init(ctx);
+	ctx = EVP_CIPHER_CTX_new();
+	if (ctx == NULL) {
+		*bytes_written = 0;
+		return;
+	}
 	EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
 	EVP_CIPHER_CTX_set_padding(ctx, 0);
 
-
 	if (verbose >= 5)
 	{
 		printbuf(iv,  16, "decrypting with this IV");
