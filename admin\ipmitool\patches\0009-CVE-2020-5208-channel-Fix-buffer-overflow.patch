From 743dd4faa302f22950e4438cf684e1e398eb47eb Mon Sep 17 00:00:00 2001
From: Chrostoper Ertl <<EMAIL>>
Date: Thu, 28 Nov 2019 16:56:38 +0000
Subject: [PATCH 09/11] channel: Fix buffer overflow
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

Partial fix for CVE-2020-5208, see
https://github.com/ipmitool/ipmitool/security/advisories/GHSA-g659-9qxw-p7cp

The `ipmi_get_channel_cipher_suites` function does not properly check
the final response’s `data_len`, which can lead to stack buffer overflow
on the final copy.
---
 lib/ipmi_channel.c | 5 ++++-
 1 file changed, 4 insertions(+), 1 deletion(-)

--- a/lib/ipmi_channel.c
+++ b/lib/ipmi_channel.c
@@ -413,7 +413,10 @@ ipmi_get_channel_cipher_suites(struct ip
 			lprintf(LOG_ERR, "Unable to Get Channel Cipher Suites");
 			return -1;
 		}
-		if (rsp->ccode > 0) {
+		if (rsp->ccode
+		    || rsp->data_len < 1
+		    || rsp->data_len > sizeof(uint8_t) + 0x10)
+		{
 			lprintf(LOG_ERR, "Get Channel Cipher Suites failed: %s",
 					val2str(rsp->ccode, completion_code_vals));
 			return -1;
