--- a/<PERSON><PERSON><PERSON>
+++ b/Makefile
@@ -89,7 +89,7 @@ build_bins: cleanup_bins
 	for f in $(BIN_FILES) ;\
 	do \
 	   sed "1d" src/bin/$$f > src/bin/$$f.bak ;\
-	   echo "#!$(LUA_BINDIR)/lua$(LUA_SUFFIX)" > src/bin/$$f ;\
+	   echo "#!/usr/bin/env lua5.1" > src/bin/$$f ;\
 	   echo "package.path = [[$(LUADIR)/?.lua;]]..package.path" | sed "s,//,/,g" >> src/bin/$$f ;\
 	   cat src/bin/$$f.bak >> src/bin/$$f ;\
 	   chmod +x src/bin/$$f ;\
