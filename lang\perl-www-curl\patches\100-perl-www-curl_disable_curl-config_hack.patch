--- a/Makefile.PL
+++ b/Makefile.PL
@@ -12,31 +12,31 @@ no_index		directory => 'template';
 repository		'http://github.com/szbalint/WWW--Curl';
 # This is a hack. If you have libcurl installed, just specify curl.h below
 # and comment out this line.
-if ($^O ne 'MSWin32') {
-    if (!$ENV{CURL_CONFIG}) {
-	    requires_external_bin	'curl-config';
-    }
-} else {
-	print	"Sorry, no automated install is available on Windows,\n".
-		"please see the README.Win32 file on instructions for a manual install.\n";
-	exit(0);
-}
-
-my $curl_config = $ENV{CURL_CONFIG} || 'curl-config';
-
-my $vernum = `${curl_config} --vernum`; chomp $vernum;
-my $version = `${curl_config} --version`; chomp $version; 
-
-my $minimum_ver = hex("070a08");
-
-if ($vernum && hex($vernum) <= $minimum_ver) {
-	print	"Your currently installed libcurl version - $version - is too old.\n".
-		"This module doesn't seek compatibility with versions older than 7.10.8\n".
-		"Proceed manually if you know what you're doing.\n";
-	exit(0);
-}
-
-print "The version is $version\n";
+#if ($^O ne 'MSWin32') {
+#    if (!$ENV{CURL_CONFIG}) {
+#	    requires_external_bin	'curl-config';
+#    }
+#} else {
+#	print	"Sorry, no automated install is available on Windows,\n".
+#		"please see the README.Win32 file on instructions for a manual install.\n";
+#	exit(0);
+#}
+#
+#my $curl_config = $ENV{CURL_CONFIG} || 'curl-config';
+#
+#my $vernum = `${curl_config} --vernum`; chomp $vernum;
+#my $version = `${curl_config} --version`; chomp $version; 
+#
+#my $minimum_ver = hex("070a08");
+#
+#if ($vernum && hex($vernum) <= $minimum_ver) {
+#	print	"Your currently installed libcurl version - $version - is too old.\n".
+#		"This module doesn't seek compatibility with versions older than 7.10.8\n".
+#		"Proceed manually if you know what you're doing.\n";
+#	exit(0);
+#}
+#
+#print "The version is $version\n";
 
 my @includes = qw();
 my ($cflags,$lflags, $ldflags) = ('','','');
@@ -58,10 +58,10 @@ if ($^O ne 'MSWin32') {
 # Get curl to tell us where it is, if we can.
 #
 
-if ($^O ne 'MSWin32') {
-	$cflags = `${curl_config} --cflags`;
-	$lflags = `${curl_config} --libs`;
-}
+#if ($^O ne 'MSWin32') {
+#	$cflags = `${curl_config} --cflags`;
+#	$lflags = `${curl_config} --libs`;
+#}
 
 # can't find link flags, make some guesses
 if (!defined($lflags)) {
