commit 098bd91f5eae625c7d2ee621e10930fc4434e5e2
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Tue Feb 26 16:16:33 2013 +0000

    gcc: don't build documentation
    
    This closes #13039.
    
    Signed-off-by: <PERSON><PERSON> <<EMAIL>>
    
    SVN-Revision: 35807

--- a/gcc/Makefile.in
+++ b/gcc/Makefile.in
@@ -3121,18 +3121,10 @@ doc/gcc.info: $(TEXI_GCC_FILES)
 doc/gccint.info: $(TEXI_GCCINT_FILES)
 doc/cppinternals.info: $(TEXI_CPPINT_FILES)
 
-doc/%.info: %.texi
-	if [ x$(BUILD_INFO) = xinfo ]; then \
-		$(MAKEINFO) $(MAKEINFOFLAGS) -I . -I $(gcc_docdir) \
-			-I $(gcc_docdir)/include -o $@ $<; \
-	fi
+doc/%.info:
 
 # Duplicate entry to handle renaming of gccinstall.info
-doc/gccinstall.info: $(TEXI_GCCINSTALL_FILES)
-	if [ x$(BUILD_INFO) = xinfo ]; then \
-		$(MAKEINFO) $(MAKEINFOFLAGS) -I $(gcc_docdir) \
-			-I $(gcc_docdir)/include -o $@ $<; \
-	fi
+doc/gccinstall.info:
 
 doc/cpp.dvi: $(TEXI_CPP_FILES)
 doc/gcc.dvi: $(TEXI_GCC_FILES)
