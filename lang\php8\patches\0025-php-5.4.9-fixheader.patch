From: Debian PHP Maintainers <<EMAIL>>
Date: Sat, 2 May 2015 10:26:56 +0200
Subject: php-5.4.9-fixheader

Make generated php_config.h constant across rebuilds.
---
 configure.ac | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

--- a/configure.ac
+++ b/configure.ac
@@ -1501,7 +1501,7 @@ PHP_REMOVE_USR_LIB(LDFLAGS)
 EXTRA_LDFLAGS="$EXTRA_LDFLAGS $PHP_LDFLAGS"
 EXTRA_LDFLAGS_PROGRAM="$EXTRA_LDFLAGS_PROGRAM $PHP_LDFLAGS"
 
-UNAME=`uname -a | xargs`
+UNAME=`uname | xargs`
 PHP_UNAME=${PHP_UNAME:-$UNAME}
 AC_DEFINE_UNQUOTED(PHP_UNAME,"$PHP_UNAME",[uname -a output])
 PHP_OS=`uname | xargs`
