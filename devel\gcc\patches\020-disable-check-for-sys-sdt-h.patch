@@ -0,0 +1,45 @@
--- a/gcc/configure
+++ b/gcc/configure
@@ -29036,19 +29036,6 @@ $as_echo "#define ENABLE_DEFAULT_SSP 1"
 fi
 
 
-# Test for <sys/sdt.h> on the target.
-
-{ $as_echo "$as_me:${as_lineno-$LINENO}: checking sys/sdt.h in the target C library" >&5
-$as_echo_n "checking sys/sdt.h in the target C library... " >&6; }
-have_sys_sdt_h=no
-if test -f $target_header_dir/sys/sdt.h; then
-  have_sys_sdt_h=yes
-
-$as_echo "#define HAVE_SYS_SDT_H 1" >>confdefs.h
-
-fi
-{ $as_echo "$as_me:${as_lineno-$LINENO}: result: $have_sys_sdt_h" >&5
-$as_echo "$have_sys_sdt_h" >&6; }
 
 # Check if TFmode long double should be used by default or not.
 # Some glibc targets used DFmode long double, but with glibc 2.4
--- a/gcc/configure.ac
+++ b/gcc/configure.ac
@@ -5787,16 +5787,6 @@ if test x$enable_default_ssp = xyes ; th
 fi
 AC_SUBST([enable_default_ssp])
 
-# Test for <sys/sdt.h> on the target.
-GCC_TARGET_TEMPLATE([HAVE_SYS_SDT_H])
-AC_MSG_CHECKING(sys/sdt.h in the target C library)
-have_sys_sdt_h=no
-if test -f $target_header_dir/sys/sdt.h; then
-  have_sys_sdt_h=yes
-  AC_DEFINE(HAVE_SYS_SDT_H, 1,
-            [Define if your target C library provides sys/sdt.h])
-fi
-AC_MSG_RESULT($have_sys_sdt_h)
 
 # Check if TFmode long double should be used by default or not.
 # Some glibc targets used DFmode long double, but with glibc 2.4
