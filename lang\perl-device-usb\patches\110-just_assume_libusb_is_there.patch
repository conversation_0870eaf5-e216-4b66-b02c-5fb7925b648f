Subject: Just assume libusb is out there
 Makefile.PL should not try to check for libusb in a given list of directories
 as the compiler might look in other places as well.
Origin: vendor
Bug-Debian: http://bugs.debian.org/639677
Forwarded: not-needed
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Reviewed-by: g<PERSON><PERSON> <<EMAIL>>
Last-Update: 2013-10-28

--- a/Makefile.PL
+++ b/Makefile.PL
@@ -21,7 +21,7 @@ END
     }
 }
 
-unless(header_found())
+unless(1 || header_found())
 {
     die <<"END";
 ERROR: Can't find usb.h header.
@@ -36,7 +36,7 @@ following environment variables:
 END
 }
 
-unless(lib_found())
+unless(1 || lib_found())
 {
     die <<"END";
 ERROR: Can't find libusb library.
