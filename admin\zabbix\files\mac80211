#see https://openwrt.org/docs/guide-user/services/network_monitoring/zabbix for ready to use templates

# If you want to know the exact meaning of an UserParameter, you can search in the ieee80211 standard:
# https://standards.ieee.org/getieee802/download/802.11-2012.pdf
# example: for mac80211.ACKFailureCount search for dot11ACKFailureCount (page 2145)

# mac80211 phy discovery (like 'phy0')
# example: {"data":[{"{#PHY}":"phy0"}]}
#
UserParameter=mac80211.phydiscovery,zabbix_helper_mac80211 discovery

#phy statistics (you need {#PHY} as parameter)
#
UserParameter=mac80211.ACKFailureCount[*],zabbix_helper_mac80211 $1 dot11ACKFailureCount
UserParameter=mac80211.FCSErrorCount[*],zabbix_helper_mac80211 $1 dot11FCSErrorCount
UserParameter=mac80211.RTSFailureCount[*],zabbix_helper_mac80211 $1 dot11RTSFailureCount
UserParameter=mac80211.RTSSuccessCount[*],zabbix_helper_mac80211 $1 dot11RTSSuccessCount

# hidden behind MAC80211_DEBUG_COUNTERS
UserParameter=mac80211.FailedCount[*],zabbix_helper_mac80211 $1 dot11FailedCount
UserParameter=mac80211.FrameDuplicateCount[*],zabbix_helper_mac80211 $1 dot11FrameDuplicateCount
UserParameter=mac80211.MulticastReceivedFrameCount[*],zabbix_helper_mac80211 $1 dot11MulticastReceivedFrameCount
UserParameter=mac80211.MulticastTransmittedFrameCount[*],zabbix_helper_mac80211 $1 dot11MulticastTransmittedFrameCount
UserParameter=mac80211.MultipleRetryCount[*],zabbix_helper_mac80211 $1 dot11MultipleRetryCount
UserParameter=mac80211.ReceivedFragmentCount[*],zabbix_helper_mac80211 $1 dot11ReceivedFragmentCount
UserParameter=mac80211.RetryCount[*],zabbix_helper_mac80211 $1 dot11RetryCount
UserParameter=mac80211.TransmittedFragmentCount[*],zabbix_helper_mac80211 $1 dot11TransmittedFragmentCount
UserParameter=mac80211.TransmittedFrameCount[*],zabbix_helper_mac80211 $1 dot11TransmittedFrameCount

