--- a/perl.c
+++ b/perl.c
@@ -1987,16 +1987,6 @@ S_Internals_V(pTHX_ CV *cv)
     PUSHs(Perl_newSVpvn_flags(aTHX_ non_bincompat_options,
 			      sizeof(non_bincompat_options) - 1, SVs_TEMP));
 
-#ifndef PERL_BUILD_DATE
-#  ifdef __DATE__
-#    ifdef __TIME__
-#      define PERL_BUILD_DATE __DATE__ " " __TIME__
-#    else
-#      define PERL_BUILD_DATE __DATE__
-#    endif
-#  endif
-#endif
-
 #ifdef PERL_BUILD_DATE
     PUSHs(Perl_newSVpvn_flags(aTHX_
 			      STR_WITH_LEN("Compiled at " PERL_BUILD_DATE),
