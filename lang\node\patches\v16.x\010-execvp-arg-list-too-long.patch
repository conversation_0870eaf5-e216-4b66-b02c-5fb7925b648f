--- a/deps/npm/node_modules/node-gyp/gyp/pylib/gyp/generator/make.py
+++ b/deps/npm/node_modules/node-gyp/gyp/pylib/gyp/generator/make.py
@@ -154,6 +154,32 @@ cmd_alink_thin = rm -f $@ && $(AR.$(TOOL
 quiet_cmd_link = LINK($(TOOLSET)) $@
 cmd_link = $(LINK.$(TOOLSET)) -o $@ $(GYP_LDFLAGS) $(LDFLAGS.$(TOOLSET)) -Wl,--start-group $(LD_INPUTS) $(LIBS) -Wl,--end-group
 
+define xargs
+        $(1) $(wordlist 1,100,$(2))
+        $(if $(word 101,$(2)),$(call xargs,$(1),$(wordlist 101,$(words $(2)),$(2))))
+endef
+
+define write-to-file
+        @echo >$(1)
+        $(call xargs,printf "%s\\n" >>$(1),$(2))
+endef
+
+OBJ_FILE_LIST_SFX := ar-file-list
+
+define create_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(1).$(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crs $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
+define create_thin_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crsT $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
 # We support two kinds of shared objects (.so):
 # 1) shared_library, which is just bundling together many dependent libraries
 # into a link line.
@@ -198,6 +224,32 @@ cmd_alink = rm -f $@ && $(AR.$(TOOLSET))
 quiet_cmd_alink_thin = AR($(TOOLSET)) $@
 cmd_alink_thin = rm -f $@ && $(AR.$(TOOLSET)) crsT $@ $(filter %.o,$^)
 
+define xargs
+        $(1) $(wordlist 1,100,$(2))
+        $(if $(word 101,$(2)),$(call xargs,$(1),$(wordlist 101,$(words $(2)),$(2))))
+endef
+
+define write-to-file
+        @echo >$(1)
+        $(call xargs,printf "%s\\n" >>$(1),$(2))
+endef
+
+OBJ_FILE_LIST_SFX := ar-file-list
+
+define create_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(1).$(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crs $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
+define create_thin_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crsT $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
 # Due to circular dependencies between libraries :(, we wrap the
 # special "figure out circular dependencies" flags around the entire
 # input list during linking.
@@ -1768,21 +1820,35 @@ $(obj).$(TOOLSET)/$(TARGET)/%%.o: $(obj)
                 self.flavor not in ("mac", "openbsd", "netbsd", "win")
                 and not self.is_standalone_static_library
             ):
-                self.WriteDoCmd(
-                    [self.output_binary],
-                    link_deps,
-                    "alink_thin",
-                    part_of_all,
-                    postbuilds=postbuilds,
-                )
+                if self.flavor in ('linux', 'android'):
+                    self.WriteMakeRule(
+                        [self.output_binary],
+                        link_deps,
+                        actions = ['$(call create_thin_archive,$@,$^)']
+                    )
+                else:
+                    self.WriteDoCmd(
+                        [self.output_binary],
+                        link_deps,
+                        "alink_thin",
+                        part_of_all,
+                        postbuilds=postbuilds,
+                    )
             else:
-                self.WriteDoCmd(
-                    [self.output_binary],
-                    link_deps,
-                    "alink",
-                    part_of_all,
-                    postbuilds=postbuilds,
-                )
+                if self.flavor in ('linux', 'android'):
+                    self.WriteMakeRule(
+                        [self.output_binary],
+                        link_deps,
+                        actions = ['$(call create_archive,$@,$^)']
+                    )
+                else:
+                    self.WriteDoCmd(
+                        [self.output_binary],
+                        link_deps,
+                        "alink",
+                        part_of_all,
+                        postbuilds=postbuilds,
+                    )
         elif self.type == "shared_library":
             self.WriteLn(
                 "%s: LD_INPUTS := %s"
--- a/tools/gyp/pylib/gyp/generator/make.py
+++ b/tools/gyp/pylib/gyp/generator/make.py
@@ -156,6 +156,32 @@ cmd_alink_thin = rm -f $@ && $(AR.$(TOOL
 quiet_cmd_link = LINK($(TOOLSET)) $@
 cmd_link = $(LINK.$(TOOLSET)) -o $@ $(GYP_LDFLAGS) $(LDFLAGS.$(TOOLSET)) -Wl,--start-group $(LD_INPUTS) $(LIBS) -Wl,--end-group
 
+define xargs
+        $(1) $(wordlist 1,100,$(2))
+        $(if $(word 101,$(2)),$(call xargs,$(1),$(wordlist 101,$(words $(2)),$(2))))
+endef
+
+define write-to-file
+        @echo >$(1)
+        $(call xargs,printf "%s\\n" >>$(1),$(2))
+endef
+
+OBJ_FILE_LIST_SFX := ar-file-list
+
+define create_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(1).$(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crs $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
+define create_thin_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crsT $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
 # We support two kinds of shared objects (.so):
 # 1) shared_library, which is just bundling together many dependent libraries
 # into a link line.
@@ -200,6 +226,32 @@ cmd_alink = rm -f $@ && $(AR.$(TOOLSET))
 quiet_cmd_alink_thin = AR($(TOOLSET)) $@
 cmd_alink_thin = rm -f $@ && $(AR.$(TOOLSET)) crsT $@ $(filter %.o,$^)
 
+define xargs
+        $(1) $(wordlist 1,100,$(2))
+        $(if $(word 101,$(2)),$(call xargs,$(1),$(wordlist 101,$(words $(2)),$(2))))
+endef
+
+define write-to-file
+        @echo >$(1)
+        $(call xargs,printf "%s\\n" >>$(1),$(2))
+endef
+
+OBJ_FILE_LIST_SFX := ar-file-list
+
+define create_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(1).$(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crs $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
+define create_thin_archive
+        $(eval OBJ_FILE_LIST := $(basename $(notdir $(1))).$(OBJ_FILE_LIST_SFX))
+        rm -f $(1) $(OBJ_FILE_LIST); mkdir -p `dirname $(1)`
+        $(call write-to-file,$(1).$(OBJ_FILE_LIST),$(filter %.o,$(2)))
+        $(AR.$(TOOLSET)) crsT $(1) @$(1).$(OBJ_FILE_LIST)
+endef
+
 # Due to circular dependencies between libraries :(, we wrap the
 # special "figure out circular dependencies" flags around the entire
 # input list during linking.
@@ -1809,21 +1861,35 @@ $(obj).$(TOOLSET)/$(TARGET)/%%.o: $(obj)
                 self.flavor not in ("mac", "openbsd", "netbsd", "win")
                 and not self.is_standalone_static_library
             ):
-                self.WriteDoCmd(
-                    [self.output_binary],
-                    link_deps,
-                    "alink_thin",
-                    part_of_all,
-                    postbuilds=postbuilds,
-                )
+                if self.flavor in ('linux', 'android'):
+                    self.WriteMakeRule(
+                        [self.output_binary],
+                        link_deps,
+                        actions = ['$(call create_thin_archive,$@,$^)']
+                    )
+                else:
+                    self.WriteDoCmd(
+                        [self.output_binary],
+                        link_deps,
+                        'alink_thin',
+                        part_of_all,
+                        postbuilds=postbuilds
+                    )
             else:
-                self.WriteDoCmd(
-                    [self.output_binary],
-                    link_deps,
-                    "alink",
-                    part_of_all,
-                    postbuilds=postbuilds,
-                )
+                if self.flavor in ('linux', 'android'):
+                    self.WriteMakeRule(
+                        [self.output_binary],
+                        link_deps,
+                        actions = ['$(call create_archive,$@,$^)']
+                    )
+                else:
+                    self.WriteDoCmd(
+                        [self.output_binary],
+                        link_deps,
+                        'alink',
+                        part_of_all,
+                        postbuilds=postbuilds
+                    )
         elif self.type == "shared_library":
             self.WriteLn(
                 "%s: LD_INPUTS := %s"
