--- a/lib/Inline/C.pm
+++ b/lib/Inline/C.pm
@@ -457,22 +457,28 @@ sub get_maps {
     print STDERR "get_maps Stage\n" if $o->{CONFIG}{BUILD_NOISY};
     my $typemap = '';
     my $file;
-    $file = File::Spec->catfile(
-        $Config::Config{installprivlib},
-        "ExtUtils",
-        "typemap",
-    );
-    $typemap = $file if -f $file;
-    $file = File::Spec->catfile(
-        $Config::Config{privlibexp}
-        ,"ExtUtils","typemap"
-    );
-    $typemap = $file
-        if (not $typemap and -f $file);
-    warn "Can't find the default system typemap file"
-        if (not $typemap and $^W);
+    
+    unless ($ENV{'_INLINE_C_SYSTEM_TYPEMAP_'}) {
+        $file = File::Spec->catfile(
+            $Config::Config{installprivlib},
+            "ExtUtils",
+            "typemap",
+        );
+        $typemap = $file if -f $file;
+        $file = File::Spec->catfile(
+            $Config::Config{privlibexp}
+            ,"ExtUtils","typemap"
+        );
+        $typemap = $file
+            if (not $typemap and -f $file);
+        warn "Can't find the default system typemap file"
+            if (not $typemap and $^W);
 
-    unshift(@{$o->{ILSM}{MAKEFILE}{TYPEMAPS}}, $typemap) if $typemap;
+        unshift(@{$o->{ILSM}{MAKEFILE}{TYPEMAPS}}, $typemap) if $typemap;
+    }
+    else {
+        unshift(@{$o->{ILSM}{MAKEFILE}{TYPEMAPS}}, $ENV{'_INLINE_C_SYSTEM_TYPEMAP_'});
+    }
 
     if (not $o->UNTAINT) {
         require FindBin;
