--- a/lib/pluginManager.js
+++ b/lib/pluginManager.js
@@ -324,6 +324,7 @@ class PluginManager {
             else {
                 this.searchPaths.add("/usr/local/lib/node_modules");
                 this.searchPaths.add("/usr/lib/node_modules");
+                this.searchPaths.add("/usr/lib/node");
                 this.searchPaths.add(child_process_1.execSync("/bin/echo -n \"$(npm --no-update-notifier -g prefix)/lib/node_modules\"").toString("utf8"));
             }
         }
