--- a/kernelmod/main.c
+++ b/kernelmod/main.c
@@ -98,8 +98,11 @@ modinit( void ) {
 
   rv = mrm_rcdb_init();
   if (rv != 0) return rv;
-
+#if LINUX_VERSION_CODE < KERNEL_VERSION(4,13,0)
   nf_register_hook(&_hops);
+#else
+  nf_register_net_hook(&init_net, &_hops);
+#endif
   mrm_init_ctlfile(); /* XXX not checking for failure! */
 
   printk(KERN_INFO "MRM The MAC Address Re-Mapper is now in the kernel\n");
@@ -110,7 +113,11 @@ modinit( void ) {
 static void __exit
 modexit( void ) {
   mrm_destroy_ctlfile();
+#if LINUX_VERSION_CODE < KERNEL_VERSION(4,13,0)
   nf_unregister_hook(&_hops);
+#else
+  nf_unregister_net_hook(&init_net, &_hops);
+#endif
   mrm_rcdb_destroy(); /* imperative that this happens last */
   printk(KERN_INFO "MRM The MAC Address Re-Mapper gone bye-bye\n");
 }
